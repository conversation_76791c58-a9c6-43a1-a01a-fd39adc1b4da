{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?d562", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?edf2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?5d87", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?97fe", "uni-app:///pages/order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?ed0a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/order.vue?5c12"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "show", "value", "list", "id", "selectedIds", "configModalShow", "currentItem", "modalData", "serviceId", "chooseArr", "chosenInputValues", "list2", "list3", "serviceInfo", "newsubbit", "form", "carId", "btArr", "yikoujiaprice", "isSubmitting", "computed", "isAllChecked", "totalPrice", "item", "total", "methods", "showdetail", "console", "loading", "loadConfigData", "res", "name", "choose", "ids", "cartData", "originalItem", "option", "formItem", "problemDesc", "val", "uni", "icon", "title", "duration", "closeConfigModal", "getFormIndex", "chooseOne", "imgUpload", "imgtype", "newFormData", "submitConfig", "copy_form", "open", "godeta<PERSON>", "url", "confirmDel", "setTimeout", "goTrash", "plus", "num", "minus", "toggleAll", "child", "toggleAllCheck", "toggleItem", "item2", "getList", "group", "allChecked", "checked", "goDown", "goToOrder", "selectedItems", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5GA;AAAA;AAAA;AAAA;AAAo1B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkKx2B;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;QACAJ;QACAK;QACAC;QACAC;QACAR;QACAS;QACAC;QACAC;QACAC;QACAC;UACAhB;UACAiB;UACAR;QACA;QACAS;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;QACAC;UACA;YACAC;UACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;;gBAEA;gBACA;kBACAxB;kBACAK;kBACAC;kBACAC;kBACAR;kBACAS;kBACAC;kBACAC;kBACAC;kBACAC;oBACAhB;oBACAiB;oBACAR;kBACA;kBACAS;kBACAC;kBACAC;kBACAS;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAF;gBAAA;gBAAA;gBAAA,OAGA;kBACAA;kBACA;oBACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA,OAGA;kBACAxB;gBACA;kBACAwB;kBACA;kBACAA;kBAEAG;oBACA;sBACA;oBACA;oBACA;sBACAP;sBACAA;wBACA;0BACAf;0BACAuB;0BACAC;wBACA;sBACA;oBACA;kBACA;kBAEA;oBAAA;kBAAA;kBACAL;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBAEA;oBAAA;kBAAA;kBACAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBAEA;oBAAA;kBAAA;kBACAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA,OAGA;kBACAM;gBACA;kBACAN;kBACA;kBACA;oBACAO;kBACA;kBACAP;kBAEA;kBACA;kBAEA;oBACAO;sBACA;wBAAA;sBAAA;sBAEA;wBACA;wBACA;0BAAA;wBAAA,MACA;0BAAA;wBAAA,MACA;0BAAA;wBAAA;wBAEA;0BACA;4BACA;8BACAC;gCACA;kCACAC;kCACA;oCAAA;kCAAA;oCACA;kCACA;gCACA;8BACA;4BACA;4BACA;8BACAC;4BACA;4BACA;8BACAA;4BACA;8BACAA;4BACA;0BACA;4BACAA;4BACA;8BACAC;8BACAC;4BACA;0BACA;4BACAF;8BAAA;4BAAA;0BACA;wBACA;sBACA;oBACA;kBACA;kBACAV;gBACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACAa;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA;QACA;UACA;UACAvB;QACA;QACA;QACA;UACAA;YACA;cACA;YACA;UACA;QACA;MACA;QAAA;QACA;QACA;UACAA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwB;MACA;QAAAC;MACA;MACAC,uDACAA;QACAV;MAAA,EACA;MACA;IACA;IAEA;IACAW;MAAA;QAAA;MACA;QACA;MACA;MAEA;MAEA;MACA;QAAA;UAAA/C;QAAA;MAAA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACAgD;QACA;MACA;MAEA;QACA;UAAA;QAAA;QACA;UACA;YACAA;UACA;UACAA;QACA;MACA;MAEA;MACAA;QACA;UAAA;QAAA;QACA;UACAX;YACAC;YACAC;YACAC;UACA;UACAS;UACA;QACA;QACA;UACA7B;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QAAA;QAAA;UACApB;UACAoC;QACA;MAAA;MAEA;QACAxC;QACAS;QACAQ;MACA;MAEAW;MAEA;QACA;UACAa;YACAC;YACAC;YACAC;UACA;UACA;UACA;UACA;QACA;UACAH;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAhB;QACAa;UACAC;UACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IAEA;IACAU;MACAb;QACAc;MACA;IACA;IACAC;MAAA;MACA;MACA5B;MACA;QACAM;MACA;QACAO;UACAC;UACAC;QACA;QACAc;UACA;QACA;MACA;IACA;IACAC;MACA9B;MACA;MACA;MACA;QAAA;MAAA;QAAA;MAAA;MACA;QACA;MACA;IACA;IACA+B;MAAA;MACA;QACAlD;QACAL;QACAwD;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACApD;QACAL;QACAwD;MACA;QACA;MACA;IACA;IACA;IACAE;MACAtC;MACAA;QACAuC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAxC;QACAA;UACAuC;QACA;MACA;IACA;IACA;IACAE;MACAC;MACA;MACA1C;QAAA;MAAA;IACA;IACA2C;MAAA;MACA;QACAvC;QACA;QACA;UACA,uCACAwC;YACAC;YACAnE;cACA,uCACAsB;gBACA8C;cAAA;YAEA;UAAA;QAEA;MACA;IACA;IACAC;MACA9B;QACAc;MACA;IACA;IACA;IACAiB;MACA;MACA;QACAhD;UACA;YACAiD;UACA;QACA;MACA;;MAEA;QACAhC;UACAE;UACAD;QACA;QACA;MACA;;MAEA;MACA;QAAAR;MAAA;MACAN;;MAEA;MACA;MACAa;QACAc;MACA;MACAd;QACAE;QACAD;MACA;IACA;EACA;EACAgC;IACA;IACA9C;IACA;MACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClqBA;AAAA;AAAA;AAAA;AAA2lD,CAAgB,+iDAAG,EAAC,C;;;;;;;;;;;ACA/mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=42e2fba5&scoped=true&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&id=42e2fba5&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"42e2fba5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=template&id=42e2fba5&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-number-box/u-number-box\" */ \"uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.configModalShow && !_vm.modalData.loading\n      ? _vm.__map(_vm.modalData.list2, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.getFormIndex(item.id)\n          var m1 = _vm.getFormIndex(item.id)\n          var m2 = _vm.getFormIndex(item.id)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            m2: m2,\n          }\n        })\n      : null\n  var l1 =\n    _vm.configModalShow && !_vm.modalData.loading\n      ? _vm.__map(_vm.modalData.list3, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.getFormIndex(item.id)\n          var m4 = _vm.getFormIndex(item.id)\n          return {\n            $orig: $orig,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var l3 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l2 = _vm.__map(item.value, function (item2, index2) {\n      var $orig = _vm.__get_orig(item2)\n      var g0 = item2.settingVals.length\n      var g1 = item2.settingVals.length\n      return {\n        $orig: $orig,\n        g0: g0,\n        g1: g1,\n      }\n    })\n    return {\n      $orig: $orig,\n      l2: l2,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<tabbar :cur=\"2\"></tabbar>\n\t\t<u-modal :show=\"show\" title=\"删除商品\" content='确认要删除该订单吗' showCancelButton @confirm=\"confirmDel\"\n\t\t\t@cancel=\"show=false\"></u-modal>\n\t\t\n\t\t<view v-if=\"configModalShow\" class=\"modal-overlay\" @click=\"closeConfigModal\">\n\t\t\t<view class=\"modal-container\" @click.stop=\"\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">修改信息</text>\n\t\t\t\t\t<text class=\"close-btn\" @click=\"closeConfigModal\">×</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view v-if=\"modalData.loading\" class=\"loading-container\">\n\t\t\t\t\t<text>加载中...</text>\n\t\t\t\t</view>\n\n\t\t\t\t<scroll-view v-else scroll-y=\"true\" class=\"modal-scroll\">\n\t\t\t\t\t<view class=\"config-content\">\n\t\t\t\t\t\t<view class=\"card\">\n\t\t\t\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t\t\t\t<view class=\"left\">已选：</view>\n\t\t\t\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t\t\t\t<view class=\"\">\n\t\t\t\t\t\t\t\t\t\t{{modalData.yikoujiaprice}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tag\" v-for=\"(item,index) in modalData.chooseArr\" :key=\"index\">{{item.name}}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tag\" v-for=\"(item,index) in modalData.chosenInputValues\" :key=\"index\">\n\t\t\t\t\t\t\t\t\t\t{{ item.problemDesc }}: {{ item.val }}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"chol\" v-for=\"(item,index) in modalData.list\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t\t\t\t<view class=\"desc\">{{item.problemContent}}</view>\n\t\t\t\t\t\t\t\t<view class=\"cho_box\">\n\t\t\t\t\t\t\t\t\t<view class=\"box_item\" v-for=\"(newItem,newIndex) in item.options\" :key=\"newIndex\"\n\t\t\t\t\t\t\t\t\t\t:style=\"newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''\"\n\t\t\t\t\t\t\t\t\t\t@click=\"chooseOne(index,newIndex,item.inputType)\">\n\t\t\t\t\t\t\t\t\t\t{{newItem.name}}\n\t\t\t\t\t\t\t\t\t\t<view class=\"ok\" :style=\"newItem.choose? '' : 'display:none;'\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"8\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"fg\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"chol\" v-for=\"item in modalData.list2\" :key=\"item.id\">\n\t\t\t\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t\t\t\t<view class=\"desc\">{{item.problemContent}}</view>\n\t\t\t\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t\t\t\t<input  \n\t\t\t\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\t\t\t\tv-model=\"modalData.form.data[getFormIndex(item.id)].val\" \n\t\t\t\t\t\t\t\t\t\t:placeholder=\"'请输入' + item.problemDesc\"\n\t\t\t\t\t\t\t\t\t\tclass=\"form-input\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"fg\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"chol\" v-for=\"(item,index) in modalData.list3\" :key=\"index\">\n\t\t\t\t\t\t\t<view class=\"choose\">\n\t\t\t\t\t\t\t\t<view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n\t\t\t\t\t\t\t\t<view class=\"desc up\">{{item.problemContent}}</view>\n\t\t\t\t\t\t\t\t<upload @upload=\"imgUpload\" @del=\"imgUpload\"\n\t\t\t\t\t\t\t\t\t:imagelist=\"modalData.form.data[getFormIndex(item.id)].val\"\n\t\t\t\t\t\t\t\t\t:imgtype=\"getFormIndex(item.id)\" text=\"上传图片\" :imgsize=\"3\">\n\t\t\t\t\t\t\t\t</upload>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"fg\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<view class=\"modal-btn cancel\" @click=\"closeConfigModal\">取消</view>\n\t\t\t\t\t<view class=\"modal-btn confirm\" \n\t\t\t\t\t\t:class=\"{ 'submitting': modalData.isSubmitting }\" \n\t\t\t\t\t\t@click=\"submitConfig\">\n\t\t\t\t\t\t{{ modalData.isSubmitting ? '提交中...' : '保存' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"\">\n\t\t\t<view class=\"car_item\" v-for=\"(item,index) in list\" :key=\"index\">\n\t\t\t\t<view class=\"trash\">\n\t\t\t\t\t<label class=\"checkbox\">\n\t\t\t\t\t\t<radio class=\"small-radio\" :checked=\"item.allChecked\" @click=\"toggleAll(item)\"\n\t\t\t\t\t\t\tcolor=\"#2979ff\" />\n\t\t\t\t\t\t<text style=\"font-size:31rpx ; font-weight: 600;\" class=\"name\">{{item.name}}</text>\n\t\t\t\t\t</label>\n\t\t\t\t\t<u-icon name=\"trash\" color=\"#2979ff\" size=\"26\" @click=\"goTrash(item)\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"divider\"></view>\n\t\t\t\t<view v-for=\"(item2,index2) in item.value\" :key=\"index2\" class=\"top\">\n\t\t\t\t\t<label class=\"checkbox\">\n\t\t\t\t\t\t<radio class=\"small-radio\" :checked=\"item2.checked\" @click=\"toggleItem(item, item2)\"\n\t\t\t\t\t\t\tcolor=\"#2979ff\" />\n\t\t\t\t\t</label>\n\t\t\t\t\t<image :src=\"item2.cover\" mode=\"\"></image>\n\t\t\t\t\t<view @click=\"showdetail(item2)\" class=\"right\">\n\t\t\t\t\t\t<div class=\"choose\">\n\t\t\t\t\t\t\t已选：\n\t\t\t\t\t\t\t<span v-for=\"(item3, index3) in item2.settingVals\" :key=\"index3\"\n\t\t\t\t\t\t\t\t:class=\"{'last-item': index3 === item2.settingVals.length - 1}\">\n\t\t\t\t\t\t\t\t{{ item3.val }}<template v-if=\"index3 !== item2.settingVals.length - 1\">,</template>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t<!-- <text v-if=\"item2.price == 0\">师傅报价</text>\n\t\t\t\t\t\t\t<text v-else>{{item2.price}}元/台起</text> -->\n\t\t\t\t\t\t\t\t<text>师傅报价</text>\n\t\t\t\t\t\t\t<u-number-box v-model=\"item2.num\" :min=\"1\">\n\t\t\t\t\t\t\t\t<template slot=\"minus\">\n\t\t\t\t\t\t\t\t\t<view @click=\"minus(item2)\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;\">\n\t\t\t\t\t\t\t\t\t\t<u-icon name=\"minus\" color=\"#333\" size=\"16\"></u-icon>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t<template slot=\"plus\">\n\t\t\t\t\t\t\t\t\t<view @click=\"plus(item2)\"\n\t\t\t\t\t\t\t\t\t\tstyle=\"width: 70rpx;height: 60rpx;background-color: #ebecee;display: flex;justify-content: center;align-items: center;\">\n\t\t\t\t\t\t\t\t\t\t<u-icon name=\"plus\" color=\"#333\" size=\"16\"></u-icon>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t</u-number-box>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"footer-left\">\n\t\t\t\t<label class=\"checkbox\">\n\t\t\t\t\t<radio class=\"large-radio\" :checked=\"isAllChecked\" @click=\"toggleAllCheck\" color=\"#2979ff\" />\n\t\t\t\t\t<text>全选</text>\n\t\t\t\t</label>\n\t\t\t</view>\n\t\t\t<!-- <view class=\"footer-center\">\n\t\t\t\t<text>合计：</text>\n\t\t\t\t<text class=\"total-price\">{{totalPrice}}元</text>\n\t\t\t</view> -->\n\t\t\t<view class=\"footer-right\" @click=\"goToOrder\">\n\t\t\t\t去下单\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport tabbar from \"@/components/tabbar.vue\"\nexport default {\n\tcomponents: {\n\t\ttabbar\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tshow: false,\n\t\t\tvalue: 1,\n\t\t\tlist: [],\n\t\t\tid: '',\n\t\t\tselectedIds: [],\n\t\t\t// 弹窗相关数据\n\t\t\tconfigModalShow: false,\n\t\t\tcurrentItem: null,\n\t\t\tmodalData: {\n\t\t\t\tid: '',\n\t\t\t\tserviceId: '',\n\t\t\t\tchooseArr: [],\n\t\t\t\tchosenInputValues: [],\n\t\t\t\tlist: [],\n\t\t\t\tlist2: [],\n\t\t\t\tlist3: [],\n\t\t\t\tserviceInfo: {},\n\t\t\t\tnewsubbit: [],\n\t\t\t\tform: {\n\t\t\t\t\tdata: [],\n\t\t\t\t\tcarId: '',\n\t\t\t\t\tserviceId: '',\n\t\t\t\t},\n\t\t\t\tbtArr: [],\n\t\t\t\tyikoujiaprice: '',\n\t\t\t\tisSubmitting: false\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 计算是否全选\n\t\tisAllChecked() {\n\t\t\treturn this.list.length > 0 && this.list.every(item => item.allChecked);\n\t\t},\n\t\t// 计算总价\n\t\ttotalPrice() {\n\t\t\tlet total = 0;\n\t\t\tthis.list.forEach(item => {\n\t\t\t\titem.value.forEach(subItem => {\n\t\t\t\t\tif (subItem.checked && subItem.price > 0) {\n\t\t\t\t\t\ttotal += subItem.price * subItem.num;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t\treturn total.toFixed(2);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 显示配置弹窗\n\t\tasync showdetail(item) {\n\t\t\tconsole.log('显示配置弹窗，item:', item);\n\t\t\tthis.currentItem = item;\n\n\t\t\t// Initialize modal data\n\t\t\tthis.modalData = {\n\t\t\t\tid: item.id,\n\t\t\t\tserviceId: item.serviceId,\n\t\t\t\tchooseArr: [],\n\t\t\t\tchosenInputValues: [],\n\t\t\t\tlist: [],\n\t\t\t\tlist2: [],\n\t\t\t\tlist3: [],\n\t\t\t\tserviceInfo: {},\n\t\t\t\tnewsubbit: [],\n\t\t\t\tform: {\n\t\t\t\t\tdata: [],\n\t\t\t\t\tcarId: item.id,\n\t\t\t\t\tserviceId: item.serviceId,\n\t\t\t\t},\n\t\t\t\tbtArr: [],\n\t\t\t\tyikoujiaprice: '',\n\t\t\t\tisSubmitting: false,\n\t\t\t\tloading: true\n\t\t\t};\n\n\t\t\t// First, show the modal\n\t\t\tthis.configModalShow = true;\n\n\t\t\t// Then, load configuration data\n\t\t\tawait this.loadConfigData();\n\t\t},\n\n\t\t// 加载配置数据\n\t\tasync loadConfigData() {\n\t\t\tconsole.log('Start loading config data, modalData.id:', this.modalData.id, 'modalData.serviceId:', this.modalData.serviceId);\n\t\t\ttry {\n\t\t\t\t// Get service info\n\t\t\t\tawait this.$api.service.getserviceInfo(this.modalData.id).then(res => {\n\t\t\t\t\tconsole.log('Get service info response:', res);\n\t\t\t\t\tif(res.data && res.data.price && res.data.price !== 0) {\n\t\t\t\t\t\tthis.modalData.yikoujiaprice = res.data.price;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.modalData.yikoujiaprice = '';\n\t\t\t\t\t}\n\t\t\t\t\tthis.modalData.serviceInfo = res.data || {};\n\t\t\t\t});\n\n\t\t\t\t// Get configuration info\n\t\t\t\tawait this.$api.service.getcartsettingsInfo({\n\t\t\t\t\tid: this.modalData.serviceId\n\t\t\t\t}).then(ress => {\n\t\t\t\t\tconsole.log('Get config info response:', ress);\n\t\t\t\t\tlet res = ress.data || [];\n\t\t\t\t\tconsole.log('Config info data:', res);\n\n\t\t\t\t\tres.forEach(item => {\n\t\t\t\t\t\tif (item.isRequired == 1) {\n\t\t\t\t\t\t\tthis.modalData.btArr.push(item.id);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (item.options) {\n\t\t\t\t\t\t\titem.options = JSON.parse(item.options);\n\t\t\t\t\t\t\titem.options = item.options.map(e => {\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\tserviceId: item.id,\n\t\t\t\t\t\t\t\t\tname: e,\n\t\t\t\t\t\t\t\t\tchoose: false\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tthis.modalData.list = res.filter(item => item.inputType == 3 || item.inputType == 4);\n\t\t\t\t\tconsole.log('Radio/Checkbox data:', this.modalData.list);\n\t\t\t\t\tthis.modalData.list.forEach((newItem) => {\n\t\t\t\t\t\tthis.modalData.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.modalData.id,\n\t\t\t\t\t\t\t\"val\": []\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\tthis.modalData.list2 = res.filter(item => item.inputType == 1);\n\t\t\t\t\tconsole.log('Input field data:', this.modalData.list2);\n\t\t\t\t\tthis.modalData.list2.forEach((newItem) => {\n\t\t\t\t\t\tthis.modalData.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.modalData.id,\n\t\t\t\t\t\t\t\"val\": ''\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\tthis.modalData.list3 = res.filter(item => item.inputType == 2);\n\t\t\t\t\tconsole.log('Image upload data:', this.modalData.list3);\n\t\t\t\t\tthis.modalData.list3.forEach((newItem) => {\n\t\t\t\t\t\tthis.modalData.form.data.push({\n\t\t\t\t\t\t\t\"serviceId\": newItem.id,\n\t\t\t\t\t\t\t\"settingId\": this.modalData.id,\n\t\t\t\t\t\t\t\"val\": []\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\t// Get cart info\n\t\t\t\tawait this.$api.service.getcartinfo({\n\t\t\t\t\tids: this.modalData.id\n\t\t\t\t}).then(ress => {\n\t\t\t\t\tconsole.log(\"Cart API Response:\", ress);\n\t\t\t\t\tlet cartData = [];\n\t\t\t\t\tif (ress.data && ress.data.length > 0 && ress.data[0].list) {\n\t\t\t\t\t\tcartData = ress.data[0].list;\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log(\"Cart Info Data:\", cartData);\n\n\t\t\t\t\tthis.modalData.chosenInputValues = [];\n\t\t\t\t\tthis.modalData.newsubbit = cartData;\n\n\t\t\t\t\tif (cartData && cartData.length > 0) {\n\t\t\t\t\t\tcartData.forEach(cartItem => {\n\t\t\t\t\t\t\tconst formItemIndex = this.modalData.form.data.findIndex(f => f.serviceId === cartItem.settingId);\n\n\t\t\t\t\t\t\tif (formItemIndex !== -1) {\n\t\t\t\t\t\t\t\tconst formItem = this.modalData.form.data[formItemIndex];\n\t\t\t\t\t\t\t\tconst originalItem = this.modalData.list.find(l => l.id === cartItem.settingId) ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t this.modalData.list2.find(l => l.id === cartItem.settingId) ||\n\t\t\t\t\t\t\t\t\t\t\t\t\t this.modalData.list3.find(l => l.id === cartItem.settingId);\n\n\t\t\t\t\t\t\t\tif (originalItem) {\n\t\t\t\t\t\t\t\t\tif (originalItem.inputType === 3 || originalItem.inputType === 4) {\n\t\t\t\t\t\t\t\t\t\tif (originalItem.options) {\n\t\t\t\t\t\t\t\t\t\t\toriginalItem.options.forEach(option => {\n\t\t\t\t\t\t\t\t\t\t\t\tif (option.name === cartItem.val) {\n\t\t\t\t\t\t\t\t\t\t\t\t\toption.choose = true;\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (!this.modalData.chooseArr.some(chosen => chosen.serviceId === option.serviceId && chosen.name === option.name)) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tthis.modalData.chooseArr.push(option);\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif (formItem.val && !Array.isArray(formItem.val)) {\n\t\t\t\t\t\t\t\t\t\t\tformItem.val = [formItem.val];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif (formItem.val && !formItem.val.includes(cartItem.val)) {\n\t\t\t\t\t\t\t\t\t\t\tformItem.val.push(cartItem.val);\n\t\t\t\t\t\t\t\t\t\t} else if (!formItem.val) {\n\t\t\t\t\t\t\t\t\t\t\tformItem.val = [cartItem.val];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else if (originalItem.inputType === 1) {\n\t\t\t\t\t\t\t\t\t\tformItem.val = cartItem.val;\n\t\t\t\t\t\t\t\t\t\tthis.modalData.chosenInputValues.push({\n\t\t\t\t\t\t\t\t\t\t\tproblemDesc: originalItem.problemDesc,\n\t\t\t\t\t\t\t\t\t\t\tval: cartItem.val\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else if (originalItem.inputType === 2) {\n\t\t\t\t\t\t\t\t\t\tformItem.val = cartItem.val ? cartItem.val.split(',').filter(url => url) : [];\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log(\"Final modalData:\", this.modalData);\n\t\t\t\t});\n\n\t\t\t\t// Data loaded\n\t\t\t\tthis.modalData.loading = false;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Failed to load config data:', error);\n\t\t\t\tthis.modalData.loading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 关闭配置弹窗\n\t\tcloseConfigModal() {\n\t\t\tthis.configModalShow = false;\n\t\t\tthis.currentItem = null;\n\t\t},\n\n\t\t// 获取表单索引\n\t\tgetFormIndex(serviceId) {\n\t\t\treturn this.modalData.form.data.findIndex(e => e.serviceId == serviceId);\n\t\t},\n\n\t\t// 选择选项\n\t\tchooseOne(i, j, inputType) {\n\t\t\tthis.modalData.list[i].options[j].choose = !this.modalData.list[i].options[j].choose;\n\t\t\tif (inputType == 3) { // Single choice\n\t\t\t\tthis.modalData.list[i].options.forEach((item, index) => {\n\t\t\t\t\tif (index == j) return;\n\t\t\t\t\titem.choose = false;\n\t\t\t\t});\n\t\t\t\tthis.modalData.chooseArr = [];\n\t\t\t\tthis.modalData.list.forEach(item => {\n\t\t\t\t\titem.options.forEach(tem => {\n\t\t\t\t\t\tif (tem.choose) {\n\t\t\t\t\t\t\tthis.modalData.chooseArr.push(tem);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t} else if (inputType == 4) { // Multiple choice\n\t\t\t\tthis.modalData.chooseArr = [];\n\t\t\t\tthis.modalData.list.forEach(item => {\n\t\t\t\t\titem.options.forEach(tem => {\n\t\t\t\t\t\tif (tem.choose) {\n\t\t\t\t\t\t\tthis.modalData.chooseArr.push(tem);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 图片上传\n\t\timgUpload(e) {\n\t\t\tlet { imagelist, imgtype } = e;\n\t\t\tlet newFormData = [...this.modalData.form.data];\n\t\t\tnewFormData[imgtype] = {\n\t\t\t\t...newFormData[imgtype],\n\t\t\t\tval: [...imagelist]\n\t\t\t};\n\t\t\tthis.$set(this.modalData.form, 'data', newFormData);\n\t\t},\n\n\t\t// 提交配置\n\t\tsubmitConfig() {\n\t\t\tif (this.modalData.isSubmitting) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.modalData.isSubmitting = true;\n\n\t\t\tlet copy_form = JSON.parse(JSON.stringify(this.modalData.form));\n\t\t\tlet copynew = JSON.parse(JSON.stringify(this.modalData.newsubbit)).map(item => ({ id: item.id }));\n\n\t\t\t// Clear previous selections\n\t\t\tthis.modalData.list.forEach(item => {\n\t\t\t\tconst formIndex = copy_form.data.findIndex(e => e.serviceId == item.id);\n\t\t\t\tif (formIndex !== -1) {\n\t\t\t\t\tcopy_form.data[formIndex].val = [];\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.modalData.chooseArr.forEach(item => {\n\t\t\t\tconst formIndex = copy_form.data.findIndex(e => e.serviceId == item.serviceId);\n\t\t\t\tif (formIndex !== -1) {\n\t\t\t\t\tif (!Array.isArray(copy_form.data[formIndex].val)) {\n\t\t\t\t\t\tcopy_form.data[formIndex].val = [];\n\t\t\t\t\t}\n\t\t\t\t\tcopy_form.data[formIndex].val.push(item.name);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tlet open = true;\n\t\t\tcopy_form.data.forEach(item => {\n\t\t\t\tlet index = this.modalData.btArr.findIndex(e => e == item.serviceId);\n\t\t\t\tif (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写完整后提交',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t\topen = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (item.val === '' || (Array.isArray(item.val) && item.val.length === 0)) {\n\t\t\t\t\titem.val = \"无\";\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif (!open) {\n\t\t\t\tthis.modalData.isSubmitting = false;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst data = copy_form.data.map((item, index) => ({\n\t\t\t\tid: copynew[index]?.id || item.serviceId,\n\t\t\t\tval: Array.isArray(item.val) ? item.val.join(',') : item.val\n\t\t\t}));\n\n\t\t\tconst payload = {\n\t\t\t\tdata,\n\t\t\t\tserviceId: this.modalData.form.serviceId,\n\t\t\t\tcarId: copy_form.data[0]?.serviceId || this.modalData.form.serviceId\n\t\t\t};\n\n\t\t\tconsole.log('payload:', payload);\n\n\t\t\tthis.$api.service.postorderinfo(payload).then(res => {\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\tthis.closeConfigModal();\n\t\t\t\t\t// Refresh list\n\t\t\t\t\tthis.getList();\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '请重新尝试',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('Submission failed:', err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: 'Network error, please try again',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}).finally(() => {\n\t\t\t\tthis.modalData.isSubmitting = false;\n\t\t\t});\n\t\t},\n\n\t\t// Original methods remain unchanged\n\t\tgodetail() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl:'../user/order_confirm'\n\t\t\t})\n\t\t},\n\t\tconfirmDel() {\n\t\t\tthis.show = false;\n\t\t\tconsole.log(this.selectedIds);\n\t\t\tthis.$api.service.discar({\n\t\t\t\tids: this.selectedIds\n\t\t\t}).then(() => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\ttitle: '删除成功'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.getList();\n\t\t\t\t}, 500);\n\t\t\t});\n\t\t},\n\t\tgoTrash(item) {\n\t\t\tconsole.log(item);\n\t\t\tthis.show = true;\n\t\t\tthis.id = item.id;\n\t\t\tthis.selectedIds = item.value.filter(subItem => subItem.checked).map(subItem => subItem.id);\n\t\t\tif (this.selectedIds.length === 0) {\n\t\t\t\tthis.selectedIds = [item.id];\n\t\t\t}\n\t\t},\n\t\tplus(item) {\n\t\t\tthis.$api.service.updatatocar({\n\t\t\t\tserviceId: item.serviceId,\n\t\t\t\tid: item.id,\n\t\t\t\tnum: item.num+1\n\t\t\t}).then(() => {\n\t\t\t\tthis.getList();\n\t\t\t});\n\t\t},\n\t\tminus(item) {\n\t\t\tif (item.num == 1) return;\n\t\t\tthis.$api.service.updatatocar({\n\t\t\t\tserviceId: item.serviceId,\n\t\t\t\tid: item.id,\n\t\t\t\tnum: item.num-1\n\t\t\t}).then(() => {\n\t\t\t\tthis.getList();\n\t\t\t});\n\t\t},\n\t\t// Select/deselect all for a single category\n\t\ttoggleAll(item) {\n\t\t\titem.allChecked = !item.allChecked;\n\t\t\titem.value.forEach(child => {\n\t\t\t\tchild.checked = item.allChecked;\n\t\t\t});\n\t\t},\n\t\t// Select/deselect all items\n\t\ttoggleAllCheck() {\n\t\t\tconst newStatus = !this.isAllChecked;\n\t\t\tthis.list.forEach(item => {\n\t\t\t\titem.allChecked = newStatus;\n\t\t\t\titem.value.forEach(child => {\n\t\t\t\t\tchild.checked = newStatus;\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// Single selection\n\t\ttoggleItem(item, item2) {\n\t\t\titem2.checked = !item2.checked;\n\t\t\t// Check if all sub-items are selected\n\t\t\titem.allChecked = item.value.every(child => child.checked);\n\t\t},\n\t\tgetList() {\n\t\t\tthis.$api.service.seecar().then(res => {\n\t\t\t\tconsole.log(res);\n\t\t\t\t// Initialize selection status\n\t\t\t\tthis.list = res.data.map(group => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...group,\n\t\t\t\t\t\tallChecked: false,\n\t\t\t\t\t\tvalue: group.value.map(item => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t\tchecked: false\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t})\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tgoDown(item) {\n\t\t\tuni.redirectTo({\n\t\t\t\turl: `../user/commodity_details?id=${item.serviceId}`\n\t\t\t});\n\t\t},\n\t\t// Go to order\n\t\tgoToOrder() {\n\t\tconst selectedItems = [];\n\t\t    this.list.forEach(item => {\n\t\t        item.value.forEach(subItem => {\n\t\t            if (subItem.checked) {\n\t\t                selectedItems.push(subItem.id); // Push only the id\n\t\t            }\n\t\t        });\n\t\t    });\n\t\t\n\t\t    if (selectedItems.length === 0) {\n\t\t        uni.showToast({\n\t\t            title: '请选择商品',\n\t\t            icon: 'none'\n\t\t        });\n\t\t        return;\n\t\t    }\n\t\t\n\t\t    // Create the desired object format\n\t\t    const payload = { ids: selectedItems };\n\t\t    console.log('Go to order', payload);\n\t\t\n\t\t    // Join ids into a comma-separated string\n\t\t    const idsString = payload.ids.join(','); // e.g., \"53,62\"\n\t\t    uni.navigateTo({\n\t\t        url: `../user/cart_play?ids=${encodeURIComponent(idsString)}`\n\t\t    });\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转下单页面',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\tonLoad() {\n\t\tlet token = uni.getStorageSync('token');\n\t\tconsole.log(111);\n\t\tif (!token || token == '') {\n\t\t\treturn;\n\t\t} else {\n\t\t\tthis.getList();\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #F8F8F8;\n\theight: 100vh;\n\toverflow: auto;\n\tpadding: 40rpx 0;\n\tpadding-bottom: 200rpx;\n\n\t.small-radio {\n\t\ttransform: scale(0.8);\n\t\ttransform-origin: center;\n\t}\n\n\t/* 调整最后一个 radio 为大尺寸 */\n\t.large-radio {\n\t\ttransform: scale(1);\n\t\ttransform-origin: center;\n\t}\n\n\t.car_item {\n\t\tmargin: 0 auto;\n\t\twidth: 686rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tpadding: 0 20rpx;\n\t\tposition: relative;\n\n\t\t.trash {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 0;\n\n\t\t\t.checkbox {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.name {\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.divider {\n\t\t\theight: 2rpx;\n\t\t\tbackground-color: #F2F3F6;\n\t\t\tmargin: 0 -20rpx;\n\t\t}\n\n\t\t.top {\n\t\t\tpadding: 36rpx 0;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tborder-bottom: 2rpx solid #F2F3F6;\n\n\t\t\t.checkbox {\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 200rpx;\n\t\t\t\theight: 200rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t}\n\n\t\t\t\t.choose {\n\t\t\t\t\tspan {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #ADADAD;\n\t\t\t\t\t\tmargin-right: 4rpx;\n\n\t\t\t\t\t\t&.last-item {\n\t\t\t\t\t\t\tmargin-right: 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.price {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tmargin-top: 68rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #E72427;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* 底部结算栏样式 */\n\t.footer {\n\t\tposition: fixed;\n\t\tbottom: calc(98rpx + env(safe-area-inset-bottom) / 2);\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 100rpx;\n\t\tbackground-color: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder-top: 1rpx solid #f2f2f2;\n\t\tpadding: 0 60rpx;\n\t\tpadding-right: 40rpx;\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n\n\t\t.footer-left {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.checkbox {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.footer-center {\n\t\t\tflex: 2;\n\t\t\ttext-align: right;\n\t\t\tpadding-right: 30rpx;\n\n\t\t\ttext {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\n\t\t\t.total-price {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #E72427;\n\t\t\t}\n\t\t}\n\n\t\t.footer-right {\n\t\t\twidth: 200rpx;\n\t\t\theight: 80rpx;\n\t\t\tbackground-color: #2979ff;\n\t\t\tcolor: #fff;\n\t\t\tborder-radius: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\t}\n}\n\n/* 弹窗样式 */\n.modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: flex-end; /* Align to the bottom */\n\tjustify-content: center;\n\tz-index: 9999;\n}\n\n.modal-container {\n\twidth: 100%; /* Full width */\n\tmax-height: 80vh; /* Max height 80% of viewport height */\n\tbackground: #fff;\n\tborder-radius: 20rpx 20rpx 0 0; /* Rounded corners only at the top */\n\tdisplay: flex;\n\tflex-direction: column;\n\toverflow: hidden;\n\ttransform: translateY(100%); /* Start off-screen at the bottom */\n\tanimation: slide-up 0.3s forwards ease-out; /* Animation for sliding up */\n}\n\n@keyframes slide-up {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f2f2f2;\n\tbackground: #fff;\n\tposition: sticky; /* Keep header at the top */\n\ttop: 0;\n\tz-index: 10;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.close-btn {\n\tfont-size: 40rpx;\n\tcolor: #999;\n\tline-height: 1;\n\tpadding: 10rpx;\n}\n\n.loading-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 100rpx;\n\tflex-grow: 1; /* Allow it to take available space */\n\n\ttext {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n}\n\n.modal-scroll {\n\tflex: 1; /* Allow scroll-view to take up remaining height */\n\toverflow-y: auto;\n}\n\n.config-content {\n\tpadding: 20rpx;\n}\n\n.card {\n\twidth: 100%;\n\tbackground: #FFFFFF;\n\tbox-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.card .bottom {\n\tdisplay: flex;\n}\n\n.card .bottom .left {\n\tfont-size: 24rpx;\n\tfont-weight: 400;\n\tcolor: #999999;\n\tpadding-top: 10rpx;\n}\n\n.card .bottom .right {\n\tflex: 1;\n\tmargin-left: 20rpx;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\talign-items: center;\n}\n\n.card .bottom .right .tag {\n\twidth: fit-content;\n\theight: 44rpx;\n\tpadding: 0 12rpx;\n\tbackground: #DCEAFF;\n\tborder-radius: 4rpx;\n\tfont-size: 16rpx;\n\tfont-weight: 400;\n\tcolor: #2E80FE;\n\tline-height: 44rpx;\n\ttext-align: center;\n\tmargin: 10rpx;\n}\n\n.chol .choose {\n\tpadding: 30rpx 0;\n}\n\n.chol .choose .title {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333333;\n}\n\n.chol .choose .title span {\n\tcolor: #E72427;\n}\n\n.chol .choose .input-container {\n\tmargin-top: 30rpx;\n\tposition: relative;\n\twidth: 100%;\n\tmin-height: 80rpx;\n}\n\n.chol .choose .form-input {\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 80rpx;\n\tbackground: #F7F7F7;\n\tborder-radius: 12rpx;\n\tpadding: 0 30rpx;\n\tfont-size: 26rpx;\n\tline-height: 80rpx;\n\tborder: 2rpx solid transparent;\n\ttransition: all 0.2s ease;\n}\n\n.chol .choose .form-input:focus {\n\tbackground: #fff;\n\tborder-color: #2E80FE;\n\tbox-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);\n\toutline: none;\n}\n\n.chol .choose .desc {\n\tmargin-top: 15rpx;\n\tfont-size: 22rpx;\n\tfont-weight: 400;\n\tcolor: #ADADAD;\n}\n\n.chol .choose .up {\n\tmargin-bottom: 30rpx;\n}\n\n.chol .choose .cho_box {\n\tmargin-top: 15rpx;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tpadding-bottom: 15rpx; /* Add some padding to the bottom */\n}\n\n.chol .choose .cho_box .box_item {\n\twidth: fit-content;\n\tpadding: 0 15rpx;\n\theight: 50rpx;\n\tbackground: #FFFFFF;\n\tborder-radius: 4rpx;\n\tborder: 2rpx solid #D8D8D8;\n\tfont-size: 22rpx;\n\tfont-weight: 400;\n\tcolor: #ADADAD;\n\tline-height: 50rpx;\n\tmargin-right: 15rpx;\n\tmargin-bottom: 15rpx;\n\tposition: relative;\n}\n\n.chol .choose .cho_box .box_item .ok {\n\twidth: 18rpx;\n\theight: 18rpx;\n\tposition: absolute;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: #2E80FE;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.chol .fg {\n\twidth: 100%;\n\theight: 15rpx;\n\tbackground: #F3F4F5;\n\tmargin: 15rpx 0;\n}\n\n.modal-footer {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tborder-top: 1rpx solid #f2f2f2;\n\tgap: 20rpx;\n\tposition: sticky; /* Keep footer at the bottom */\n\tbottom: 0;\n\tbackground: #fff;\n\tz-index: 10;\n}\n\n.modal-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: 400;\n\n\t&.cancel {\n\t\tbackground: #f5f5f5;\n\t\tcolor: #666;\n\t}\n\n\t&.confirm {\n\t\tbackground: #2e80fe;\n\t\tcolor: #ffffff;\n\t\ttransition: all 0.2s ease;\n\n\t\t&.submitting {\n\t\t\tbackground: #8bb8ff;\n\t\t\topacity: 0.7;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&id=42e2fba5&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&id=42e2fba5&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755682893972\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}