# APP师傅端401错误处理修复

## 问题描述
APP师傅端在调用`coach/grade/info`等API时收到401错误，但没有像APP用户端那样正确处理，导致用户体验不一致。

## 错误信息
```json
{
  "error": "Unauthorized",
  "message": "会话已失效，请重新登录",
  "path": "/ims/api/coach/grade/info",
  "status": 401,
  "timestamp": "2025-08-20T08:16:42.879Z"
}
```

## 修复内容

### 1. 修改 `utils/req.js` 中的401处理逻辑

#### 原逻辑问题
- 对不同API的401处理不一致
- 缺少用户友好的提示
- 没有正确的页面导航

#### 修复后的逻辑
```javascript
// 响应拦截
if (data.status === 401) {
  console.log('401 Unauthorized response for URL:', url);

  // For specific endpoints that should handle 401 themselves, return the error directly
  if (url.includes('user/info') || url.includes('coach/grade/info')) {
    console.log('401 on specific endpoint - returning error to caller');
    return reject({
      status: 401,
      error: 'Unauthorized',
      message: '会话已失效，请重新登录',
      path: url,
      timestamp: new Date().toISOString()
    });
  }

  // For other endpoints, handle 401 automatically (except excluded ones)
  if (!url.includes('activityOrder/commentActivityOrder') && 
      !url.includes('activityOrder/selectActivityConfig') && 
      !url.includes('activityOrder/selectCount') && 
      !url.includes('agents/list') && 
      !url.includes('shiFu/index/shiFuAuth')) {
    
    if (!hasHandled401) {
      hasHandled401 = true;
      console.log('401 error: Clearing state and navigating to /pages/mine');

      // Clear Vuex user state immediately
      $store.commit('updateUserItem', { key: 'userInfo', val: {} });
      $store.commit('updateUserItem', { key: 'autograph', val: '' });

      // Clear local storage
      ['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'shiInfo'].forEach(key => {
        uni.removeStorageSync(key);
      });

      // Show login prompt
      uni.showToast({
        title: '请登录',
        icon: 'none',
        duration: 2000
      });

      // Navigate to mine.vue
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/mine',
          success: () => {
            console.log('Navigated to /pages/mine');
            hasHandled401 = false; // Reset flag
          },
          fail: (err) => {
            console.error('Navigation failed:', err);
            hasHandled401 = false;
          }
        });
      }, 2000);
    }
    return reject('登录失效');
  }
}
```

### 2. 修改 `pages/mine.vue` 中的401处理

#### 在 `getmyGrade` 方法中添加401处理
```javascript
} catch (err) {
  console.error('获取师傅等级失败:', err);
  
  // 如果是401错误，清除登录状态
  if (err.status === 401 || (err.data && err.data.code === 401)) {
    console.log('师傅等级接口401错误，清除登录状态');
    this.handleInvalidSession();
    return null;
  }
  
  // 如果接口失败，保持使用缓存的值
  if (!this.labelName) {
    this.labelName = uni.getStorageSync('labelName') || '';
  }
  return null;
}
```

#### 在 `fetchUserInfoFromServer` 方法中的401处理
```javascript
} catch (error) {
  console.error('从服务器获取用户信息失败:', error);
  // 如果是401错误（未授权），清除登录状态
  if (error.status === 401 || (error.data && error.data.code === 401)) {
    console.log('token已失效，清除登录状态');
    this.handleInvalidSession();
  } else {
    // 其他错误，暂时不清除登录状态，可能是网络问题
    console.log('网络错误，暂时保持登录状态');
  }
}
```

## 修复效果

### 1. 统一的401处理
- 与APP用户端保持一致的处理逻辑
- 显示"请登录"提示
- 自动导航到登录页面
- 正确清除登录状态

### 2. 特定API的401处理
- `coach/grade/info` 等特定API的401错误会被正确捕获
- 调用方可以决定如何处理401错误
- 避免重复的401处理

### 3. 用户体验改善
- 会话失效时有明确的提示
- 自动跳转到登录页面
- 避免用户困惑

## 测试建议

1. **模拟401错误**
   - 使用过期的token调用API
   - 检查是否正确显示"请登录"提示
   - 验证是否正确跳转到mine页面

2. **检查特定API**
   - 测试`coach/grade/info`接口的401处理
   - 确认错误被正确传递给调用方

3. **验证状态清除**
   - 检查Vuex状态是否被清除
   - 验证本地存储是否被清除
   - 确认用户需要重新登录

## 与APP用户端的一致性

现在APP师傅端的401处理逻辑与APP用户端完全一致：
- ✅ 相同的错误提示
- ✅ 相同的页面导航逻辑
- ✅ 相同的状态清除机制
- ✅ 相同的防重复处理机制
