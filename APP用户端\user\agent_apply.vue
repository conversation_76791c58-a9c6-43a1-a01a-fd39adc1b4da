<template>
	<view class="page">
		<u-picker :show="show" :columns="columns" @cancel="show = false" @confirm="confirmType" keyName="title"></u-picker>
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler" keyName="title" @cancel="showCity = false" @confirm="confirmCity"></u-picker>
		<view class="header" :style="'color:'+arr[status].color" v-if="status !== ''">{{arr[status].text}}</view>
		<view class="main">
			<view class="main_item">
				<view class="title"><span>*</span>法人姓名</view>
				<input type="text" v-model="form.legalPersonName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>法人身份证号</view>
				<input type="text" v-model="form.legalPersonIdCard" placeholder="请输入身份证号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>联系电话</view>
				<input type="text" v-model="form.legalPersonTel" placeholder="请输入联系电话">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择省市区代理</view>
				<input type="text" v-model="form.typename" placeholder="请选择代理级别" disabled @click="show = true">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择区域</view>
				<input type="text" v-model="form.city" placeholder="请选择代理区域" disabled @click="showCity = true">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传法人身份证照片</view>
				<view class="card">
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg1" imgtype="legalPersonIdCardImg1" imgclass="id_card_box" text="身份证人像面" :imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄人像面</view>
					</view>
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg2" imgtype="legalPersonIdCardImg2" imgclass="id_card_box" text="身份证国徽面" :imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄国徽面</view>
					</view>
				</view>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传营业执照照片</view>
				<view class="big">
					<view class="top">
						<view class="das">
							<view class="up">
								<upload @upload="imgUpload" :imagelist="form.legalPersonLicense" imgtype="legalPersonLicense" imgclass="id_yy_box" text="营业执照" :imgsize="1"></upload>
							</view>
						</view>
					</view>
					<view class="bottom">拍摄营业执照</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="submit" v-if="status !== 1 && status !== 0">立即提交</view>
		</view>
	</view>
</template>

<script>
	// import upload from '@/user/components/upload.vue';
	export default {
		// components: {
		//     upload
		// },
		data() {
			return {
				status: '',
				loading: false,
				applyInfo: null,
				arr: [{
					text: '信息审核中，请稍作等待',
					color: '#FE921B'
				}, {
					text: '审核成功',
					color: '#07C160'
				}, {
					text: '审核失败',
					color: '#E72427'
				}],
				form: {
					typename: '省级',
					type: 1,
					city: "",
					cityId: [],
					legalPersonName: '',
					legalPersonIdCard: '',
					legalPersonTel: '',
					legalPersonIdCardImg1: [],
					legalPersonIdCardImg2: [],
					legalPersonLicense: []
				},
				showMoney: false,
				show: false,
				columns: [
					[{
						title: '省级',
						value: '1'
					}, {
						title: '市级',
						value: '2'
					}, {
						title: '区/县级代理',
						value: '3'
					}]
				],
				columnsCity: [
					[]
				],
				showCity: false
			}
		},
		watch: {
			"form.type": {
				handler(nval) {
					this.form.city = '';
					this.form.cityId = [];
					this.columnsCity = [[]];
					if (nval == 2) {
						this.columnsCity = [[], []];
					} else if (nval == 3) {
						this.columnsCity = [[], [], []];
					}
					this.getcity(0);
				}
			}
		},
		methods: {
			getcity(e) {
				this.loading = true;
				this.$api.service.getCity(e).then(res => {
					if (res.code !== '200' || !Array.isArray(res.data)) {
						console.error('Invalid city data:', res);
						uni.showToast({
							title: '获取城市数据失败',
							icon: 'none'
						});
						this.columnsCity = [[]];
						this.loading = false;
						return;
					}

					const normalizeData = (data) => {
						return data.map(item => ({
							title: item.trueName || item.title || item.name,
							id: item.id,
							pid: item.pid,
							children: item.children && Array.isArray(item.children) ? normalizeData(item.children) : []
						}));
					};

					const provinces = normalizeData(res.data);
					this.columnsCity[0] = provinces;
					console.log('Provinces loaded:', provinces);

					if (this.form.type > 1 && provinces.length > 0) {
						const firstProvince = provinces[0];
						if (firstProvince.children.length > 0) {
							this.columnsCity[1] = firstProvince.children;
							if (this.form.type > 2 && firstProvince.children[0].children.length > 0) {
								this.columnsCity[2] = firstProvince.children[0].children;
							}
						} else {
							this.$api.service.getCity(firstProvince.id).then(res1 => {
								const cities = normalizeData(res1.data || res1);
								this.columnsCity[1] = cities;
								if (this.form.type > 2 && cities.length > 0) {
									this.$api.service.getCity(cities[0].id).then(res2 => {
										const districts = normalizeData(res2.data || res2);
										this.columnsCity[2] = districts;
									}).catch(err => {
										console.error('Error loading districts:', err);
										this.columnsCity[2] = [];
									});
								}
							}).catch(err => {
								console.error('Error loading cities:', err);
								this.columnsCity[1] = [];
							});
						}
					}
					// Ensure picker is updated after data is set
					setTimeout(() => {
						if (this.$refs.uPicker) {
							this.$refs.uPicker.setColumnValues(0, this.columnsCity[0]);
							if (this.form.type > 1 && this.columnsCity[1]) {
								this.$refs.uPicker.setColumnValues(1, this.columnsCity[1]);
							}
							if (this.form.type > 2 && this.columnsCity[2]) {
								this.$refs.uPicker.setColumnValues(2, this.columnsCity[2]);
							}
						}
						this.loading = false;
					}, 100);
				}).catch(err => {
					console.error('getcity API error:', err);
					uni.showToast({
						title: '获取城市数据失败',
						icon: 'none'
					});
					this.columnsCity = [[]];
					this.loading = false;
				});
			},
			confirmCity(Array) {
				if (!Array.value || Array.value.length === 0) {
					uni.showToast({
						title: '请选择完整区域',
						icon: 'none'
					});
					return;
				}
				const levelCount = parseInt(this.form.type);
				const cityNames = [];
				const cityIds = [];

				for (let i = 0; i < levelCount; i++) {
					const selectedItem = Array.value[i];
					if (selectedItem) {
						cityNames.push(selectedItem.title);
						cityIds.push(selectedItem.id);
					} else if (this.columnsCity[i] && this.columnsCity[i][0]) {
						cityNames.push(this.columnsCity[i][0].title);
						cityIds.push(this.columnsCity[i][0].id);
					} else {
						uni.showToast({
							title: '区域数据不完整',
							icon: 'none'
						});
						return;
					}
				}

				this.form.city = cityNames.join('-') || '未选择区域';
				this.form.cityId = cityIds;
				console.log('Selected city:', this.form.city);
				console.log('Selected cityId:', this.form.cityId);
				this.showCity = false;
			},
			confirmType(Array) {
				this.form.typename = Array.value[0].title;
				this.form.type = Array.value[0].value;
				this.show = false;
			},
			changeHandler(e) {
				if (this.form.type == 1) return;
				const { columnIndex, index, picker = this.$refs.uPicker } = e;
				if (columnIndex === 0) {
					const selectedProvince = this.columnsCity[0][index];
					if (selectedProvince) {
						if (selectedProvince.children.length > 0) {
							this.columnsCity[1] = selectedProvince.children;
							picker.setColumnValues(1, selectedProvince.children);
							if (this.form.type > 2 && selectedProvince.children[0].children.length > 0) {
								this.columnsCity[2] = selectedProvince.children[0].children;
								picker.setColumnValues(2, selectedProvince.children[0].children);
							} else if (this.form.type > 2) {
								this.columnsCity[2] = [];
								picker.setColumnValues(2, []);
							}
						} else {
							this.$api.service.getCity(selectedProvince.id).then(res => {
								const cities = (res.data || res).map(item => ({
									title: item.trueName || item.title || item.name,
									id: item.id,
									pid: item.pid,
									children: item.children || []
								}));
								this.columnsCity[1] = cities;
								picker.setColumnValues(1, cities);
								if (this.form.type > 2 && cities.length > 0) {
									if (cities[0].children.length > 0) {
										this.columnsCity[2] = cities[0].children;
										picker.setColumnValues(2, cities[0].children);
									} else {
										this.$api.service.getCity(cities[0].id).then(res1 => {
											const districts = (res1.data || res1).map(item => ({
												title: item.trueName || item.title || item.name,
												id: item.id,
												pid: item.pid
											}));
											this.columnsCity[2] = districts;
											picker.setColumnValues(2, districts);
										}).catch(err => {
											console.error('Error loading districts:', err);
											this.columnsCity[2] = [];
											picker.setColumnValues(2, []);
										});
									}
								}
							}).catch(err => {
								console.error('Error loading cities:', err);
								this.columnsCity[1] = [];
								picker.setColumnValues(1, []);
							});
						}
					}
				} else if (columnIndex === 1 && this.form.type > 2) {
					const selectedCity = this.columnsCity[1][index];
					if (selectedCity) {
						if (selectedCity.children.length > 0) {
							this.columnsCity[2] = selectedCity.children;
							picker.setColumnValues(2, selectedCity.children);
						} else {
							this.$api.service.getCity(selectedCity.id).then(res => {
								const districts = (res.data || res).map(item => ({
									title: item.trueName || item.title || item.name,
									id: item.id,
									pid: item.pid
								}));
								this.columnsCity[2] = districts;
								picker.setColumnValues(2, districts);
							}).catch(err => {
								console.error('Error loading districts:', err);
								this.columnsCity[2] = [];
								picker.setColumnValues(2, []);
							});
						}
					}
				}
			},
			submit() {
				for (let key in this.form) {
					if (this.form[key] === '' || this.form[key] === '未选择区域') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						});
						return;
					} else if (typeof this.form[key] === 'object' && this.form[key].length === 0) {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						});
						return;
					}
				}
				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (!p.test(this.form.legalPersonIdCard)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号'
					});
					return;
				}
				let phoneReg = /^1[3456789]\d{9}$/;
				if (!phoneReg.test(this.form.legalPersonTel)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1000
					});
					return;
				}
				let subForm = JSON.parse(JSON.stringify(this.form));
				subForm.legalPersonIdCardImg1 = subForm.legalPersonIdCardImg1[0].path;
				subForm.legalPersonIdCardImg2 = subForm.legalPersonIdCardImg2[0].path;
				subForm.legalPersonLicense = subForm.legalPersonLicense[0].path;
				delete subForm.city;
				delete subForm.typename;
				console.log(subForm);
				this.$api.service.dlApply(subForm).then(res => {
					console.log(res);
					if (res.code === '200') {
						uni.showToast({
										title: res.msg,
										icon: "success",
										duration: 3000 // Change the duration to 3000ms
									});
									// Change the navigation here
									// Use uni.switchTab if /pages/mine is a tab bar page
									uni.switchTab({
										url: '/pages/mine'
									});
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
						});
					}
				});
			},
			imgUpload(e) {
				let { imagelist, imgtype } = e;
				this.form[imgtype] = imagelist;
			},
			seeDetails() {
				this.$api.service.dlSee().then(res => {
					if (res && res.data) {
						let obj = res.data;
						console.log('代理申请详情数据:', obj);
						this.status = obj.status;
						obj.legalPersonIdCardImg1 = obj.legalPersonIdcardImg1 ? [{ path: obj.legalPersonIdcardImg1 }] : [];
						obj.legalPersonIdCardImg2 = obj.legalPersonIdcardImg2 ? [{ path: obj.legalPersonIdcardImg2 }] : [];
						obj.legalPersonLicense = obj.legalPersonLicense ? [{ path: obj.legalPersonLicense }] : [];
						this.form = {
							...this.form,
							legalPersonName: obj.legalPersonName || '',
							legalPersonIdCard: obj.legalPersonIdcard || '',
							legalPersonTel: obj.legalPersonTel || '',
							type: obj.type || 1,
							cityId: obj.cityId ? obj.cityId.split(',') : [],
							legalPersonIdCardImg1: obj.legalPersonIdCardImg1,
							legalPersonIdCardImg2: obj.legalPersonIdCardImg2,
							legalPersonLicense: obj.legalPersonLicense
						};
						if (this.form.type && this.form.type >= 1 && this.form.type <= 3) {
							this.form.typename = this.columns[0][this.form.type - 1].title;
						}
						if (this.form.cityId && this.form.cityId.length > 0) {
							this.setCityDisplayName();
						}
					}
				}).catch(err => {
					console.log('获取代理申请详情失败:', err);
				});
			},
			setCityDisplayName() {
				if (!this.form.cityId || this.form.cityId.length === 0) {
					this.form.city = '';
					return;
				}
				const fetchCityName = async (ids) => {
					const names = [];
					let currentData = await this.$api.service.getCity(0).then(res => res.data || res).catch(() => []);
					for (let id of ids) {
						const item = currentData.find(d => d.id == id);
						if (item) {
							names.push(item.trueName);
							currentData = item.children || [];
							if (!currentData.length && ids.indexOf(id) < ids.length - 1) {
								currentData = await this.$api.service.getCity(id).then(res => res.data || res).catch(() => []);
							}
						} else {
							break;
						}
					}
					return names.join('-') || '未选择区域';
				};
				fetchCityName(this.form.cityId).then(name => {
					this.form.city = name;
					// Force update to ensure UI reflects the change
					this.$forceUpdate();
				}).catch(err => {
					console.error('Error setting city display name:', err);
					this.form.city = '未选择区域';
					this.$forceUpdate();
				});
			}
		},
		onLoad() {
			this.seeDetails();
		},
		mounted() {
			// Ensure city data is loaded after component is fully mounted
			setTimeout(() => {
				this.getcity(0);
			}, 100);
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding-bottom: 200rpx;
		.header {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			line-height: 58rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
		}
		.main {
			padding: 40rpx 30rpx;
			.main_item {
				margin-bottom: 20rpx;
				.title {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					span {
						color: #E72427;
					}
				}
				input {
					width: 690rpx;
					height: 110rpx;
					background: #F8F8F8;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 110rpx;
					padding: 0 40rpx;
					box-sizing: border-box;
				}
				.big {
					width: 690rpx;
					height: 388rpx;
					background: #F2FAFE;
					border-radius: 16rpx 16rpx 16rpx 16rpx;
					.top {
						height: 322rpx;
						padding-top: 20rpx;
						.das {
							margin: 0 auto;
							width: 632rpx;
							height: 284rpx;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
							border: 2rpx dashed #2E80FE;
							padding-top: 14rpx;
							.up {
								margin: 0 auto;
								width: 594rpx;
								height: 258rpx;
								background: rgba(0, 0, 0, 0.4);
								border-radius: 12rpx 12rpx 12rpx 12rpx;
							}
						}
					}
					.bottom {
						height: 66rpx;
						width: 690rpx;
						background: #2E80FE;
						border-radius: 0rpx 0rpx 16rpx 16rpx;
						font-size: 28rpx;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 66rpx;
						text-align: center;
					}
				}
				.card {
					display: flex;
					align-items: center;
					justify-content: space-between;
					.card_item {
						width: 332rpx;
						height: 332rpx;
						background: #F2FAFE;
						border-radius: 16rpx 16rpx 16rpx 16rpx;
						overflow: hidden;
						.top {
							height: 266rpx;
							width: 100%;
							padding-top: 40rpx;
							.das {
								margin: 0 auto;
								width: 266rpx;
								height: 180rpx;
								border: 2rpx dashed #2E80FE;
								padding-top: 28rpx;
								.up {
									margin: 0 auto;
									width: 210rpx;
									height: 130rpx;
								}
							}
						}
						.bottom {
							height: 66rpx;
							width: 100%;
							background-color: #2E80FE;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							text-align: center;
							line-height: 66rpx;
						}
					}
				}
			}
		}
		.footer {
			padding: 52rpx 30rpx;
			width: 750rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
			position: fixed;
			bottom: 0;
			.btn {
				width: 690rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
			}
		}
	}
</style>