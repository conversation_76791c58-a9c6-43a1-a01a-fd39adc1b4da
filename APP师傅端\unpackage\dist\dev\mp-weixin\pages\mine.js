(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/mine"],{

/***/ 216:
/*!********************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/main.js?{"page":"pages%2Fmine"} ***!
  \********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _mine = _interopRequireDefault(__webpack_require__(/*! ./pages/mine.vue */ 217));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_mine.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 217:
/*!***************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mine.vue?vue&type=template&id=ef6e6e68& */ 218);
/* harmony import */ var _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mine.vue?vue&type=script&lang=js& */ 220);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mine.vue?vue&type=style&index=0&lang=scss& */ 222);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 67);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["render"],
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/mine.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 218:
/*!**********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=template&id=ef6e6e68& ***!
  \**********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=template&id=ef6e6e68& */ 219);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_template_id_ef6e6e68___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 219:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=template&id=ef6e6e68& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 794))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 220:
/*!****************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js& */ 221);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 221:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 48);
var _locationManager = _interopRequireDefault(__webpack_require__(/*! @/utils/location-manager.js */ 56));
var _objectSpread2;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var tabbar = function tabbar() {
  __webpack_require__.e(/*! require.ensure | components/tabbarsf */ "components/tabbarsf").then((function () {
    return resolve(__webpack_require__(/*! @/components/tabbarsf.vue */ 819));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
// Utility function for debouncing
var debounce = function debounce(func, wait) {
  var timeout;
  return function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    var context = this;
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      return func.apply(context, args);
    }, wait);
  };
};
var _default = {
  components: {
    tabbar: tabbar
  },
  data: function data() {
    return {
      isLoading: false,
      // 确保初始状态不是loading
      inviteCode: '',
      tmplIds: [' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo', 'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs', 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'],
      code: '',
      labelName: '',
      loginPopupVisible: false,
      agreedToTerms: false,
      shifustatus: -1,
      // Initialize with a default numeric value
      // 绑定手机号相关
      bindPhonePopupVisible: false,
      isBindingPhone: false,
      bindPhoneSmsCountdown: 0,
      bindPhoneSmsTimer: null,
      bindPhoneForm: {
        phone: '',
        code: ''
      },
      orderList: [{
        icon: 'order',
        text: '全部',
        url: '/shifu/master_my_order?tab=0',
        count: 0
      }, {
        icon: 'bell',
        text: '待上门',
        url: '/shifu/master_my_order?tab=3',
        count: 0
      }, {
        icon: 'hourglass-half-fill',
        text: '待服务',
        url: '/shifu/master_my_order?tab=5',
        count: 0
      }, {
        icon: 'clock',
        text: '服务中',
        url: '/shifu/master_my_order?tab=6',
        count: 0
      }, {
        icon: 'thumb-up',
        text: '已完成',
        url: '/shifu/master_my_order?tab=7',
        count: 0
      }, {
        icon: 'chat-fill',
        text: '售后',
        url: '/shifu/master_my_order?tab=8',
        count: 0
      }],
      orderList3: [{
        icon: 'red-packet',
        text: '服务收入',
        url: '/shifu/income'
      }, {
        icon: 'file-text-fill',
        text: '报价列表',
        url: '/shifu/master_bao_list'
      }, {
        icon: 'rmb-circle',
        text: '保证金',
        url: '/shifu/Margin'
      }, {
        icon: 'account-fill',
        text: '师傅等级',
        url: '/shifu/shifuGrade'
      }],
      toolList2: [{
        icon: 'plus-people-fill',
        text: '师傅入驻',
        url: '/shifu/Settle',
        iconColor: '#448cfb'
      }, {
        icon: 'edit-pen',
        text: '编辑师傅资料',
        url: '/shifu/master_Info',
        iconColor: '#448cfb'
      }]
    };
  },
  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapState)({
    // Changed from storeUserInfo to userInfo based on user's request and Vuex module
    userInfo: function userInfo(state) {
      return state.user.userInfo || {};
    },
    token: function token(state) {
      return state.user.autograph || '';
    },
    erweima: function erweima(state) {
      return state.user.erweima || '';
    },
    regeocode: function regeocode(state) {
      return state.service.regeocode;
    }
  })), {}, {
    isLoggedIn: function isLoggedIn() {
      // 如果有token，就认为已登录，即使用户信息还在加载中
      // 这样可以避免在微信模拟器中因为用户信息加载延迟导致的登录状态丢失
      var hasToken = !!this.token;

      // 只在调试模式下打印日志，避免过多的控制台输出
      if (true) {
        console.log('isLoggedIn检查:', {
          hasToken: hasToken,
          result: hasToken
        });
      }
      return hasToken;
    },
    // This computed property will combine Vuex userInfo with local storage `shiInfo`
    // for display purposes, giving `shiInfo` priority where it makes sense.
    displayUserInfo: function displayUserInfo() {
      var shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
      console.log('displayUserInfo 计算开始:', {
        isLoggedIn: this.isLoggedIn,
        vuexUserInfo: this.userInfo,
        shiInfo: shiInfo,
        token: this.token
      });

      // 优先使用有效的师傅信息，如果师傅信息为空则使用用户信息
      var avatarUrl = shiInfo.avatarUrl && shiInfo.avatarUrl.trim() ? shiInfo.avatarUrl : this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png';
      var nickName = shiInfo.coachName && shiInfo.coachName.trim() ? shiInfo.coachName : this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户';
      var phone = shiInfo.mobile && shiInfo.mobile.trim() ? shiInfo.mobile : this.userInfo.phone || uni.getStorageSync('phone') || '';
      var userId = shiInfo.userId || this.userInfo.userId || uni.getStorageSync('userId') || '';
      var pid = shiInfo.pid || this.userInfo.pid || uni.getStorageSync('pid') || '';
      var shifuId = shiInfo.shifuId || '';
      var result = {
        phone: this.isLoggedIn ? phone : '',
        avatarUrl: this.isLoggedIn ? avatarUrl : '/static/mine/default_user.png',
        nickName: this.isLoggedIn ? nickName : '微信用户',
        userId: this.isLoggedIn ? userId : '',
        shifuId: this.isLoggedIn ? shifuId : '',
        pid: this.isLoggedIn ? pid : ''
      };
      console.log('displayUserInfo计算结果:', result);
      return result;
    },
    statusText: function statusText() {
      var _this = this;
      console.log('statusText 计算开始:', {
        isLoggedIn: this.isLoggedIn,
        shifustatus: this.shifustatus,
        cachedShiInfo: uni.getStorageSync('shiInfo')
      });

      // 如果用户未登录，不显示状态信息
      if (!this.isLoggedIn) {
        console.log('用户未登录，返回空状态');
        return '';
      }

      // 优先从本地缓存获取状态，提高响应速度
      var cachedShiInfo = uni.getStorageSync('shiInfo');
      var status = this.shifustatus;
      if (cachedShiInfo) {
        try {
          var parsedShiInfo = JSON.parse(cachedShiInfo);
          if (parsedShiInfo.status !== undefined && parsedShiInfo.status !== null) {
            status = parsedShiInfo.status;
            // 如果本地状态与当前状态不一致，更新当前状态
            if (this.shifustatus !== status) {
              this.shifustatus = status;
              console.log('从缓存同步师傅状态:', status);
            }
          }
        } catch (error) {
          console.error('解析本地师傅信息失败:', error);
        }
      }

      // 确保状态是数字类型
      status = Number(status);
      console.log('最终使用的状态值:', status);

      // 如果状态仍然无效，设置默认状态并触发获取（仅在已登录时）
      if (isNaN(status) || status === undefined || status === null) {
        console.log('检测到无效状态，设置为默认状态');
        status = -1; // 默认为未入驻

        // 只有在已登录时才触发获取师傅信息
        if (this.isLoggedIn && !this._fetchingStatus) {
          console.log('用户已登录，触发获取师傅信息');
          this._fetchingStatus = true;
          this.$nextTick(function () {
            _this.fetchShifuInfoImmediately().finally(function () {
              _this._fetchingStatus = false;
            });
          });
        }
      }
      var statusTextResult = '';
      switch (status) {
        case -1:
          statusTextResult = '未入驻师傅';
          break;
        case 1:
          statusTextResult = '审核中';
          break;
        case 2:
          statusTextResult = '已认证';
          break;
        case 4:
          statusTextResult = '审核驳回';
          break;
        default:
          console.log('未知的师傅状态值:', status);
          statusTextResult = '未入驻师傅';
        // 默认显示未入驻
      }

      console.log('statusText 计算结果:', statusTextResult);
      return statusTextResult;
    },
    statusBadgeClass: function statusBadgeClass() {
      return {
        'status-not-registered': this.shifustatus === -1,
        'status-pending': this.shifustatus === 1,
        'status-approved': this.shifustatus === 2,
        'status-rejected': this.shifustatus === 4
      };
    },
    canBindPhone: function canBindPhone() {
      return this.bindPhoneForm.phone && this.bindPhoneForm.code && this.validatePhone(this.bindPhoneForm.phone);
    }
  }),
  watch: {
    // Watch for changes in the Vuex userInfo and trigger updates
    userInfo: {
      handler: function handler(newVal, oldVal) {
        var _this2 = this;
        // Only update if there's a meaningful change to avoid infinite loops
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.saveUserInfoToStorage(newVal);
          // Force update if necessary, though Vue's reactivity should handle most cases
          this.$nextTick(function () {
            _this2.$forceUpdate();
          });
        }
      },
      deep: true,
      // Watch for nested property changes
      immediate: true // Run the handler immediately on component mount
    }
  },
  onLoad: function onLoad(options) {
    var _this3 = this;
    this.getNowPosition();
    // 先从缓存加载 labelName，避免显示延迟
    this.loadCachedLabelName();
    this.getmyGrade();
    if (options.inviteCode) {
      console.log('Received inviteCode:', options.inviteCode);
      this.inviteCode = options.inviteCode;
      uni.setStorageSync('receivedInviteCode', options.inviteCode);
    }
    if (this.erweima) {
      console.log('erweima from Vuex:', this.erweima);
      this.inviteCode = this.erweima;
      uni.setStorageSync('receivedInviteCode', this.erweima);
    } else {
      var erweima = uni.getStorageSync('erweima');
      if (erweima) {
        console.log('erweima from storage:', erweima);
        this.$store.commit('setErweima', erweima);
        this.inviteCode = erweima;
        uni.setStorageSync('receivedInviteCode', erweima);
      }
    }

    // 初始化用户数据
    this.initUserData();
    uni.login({
      provider: 'weixin',
      success: function success(res) {
        if (res.code) {
          _this3.code = res.code;
          console.log('Initial wx.login code:', _this3.code);
        }
      },
      fail: function fail(err) {
        console.error('wx.login failed:', err);
      }
    });

    // 只有在确认有token时才进行后续操作，避免不必要的loading状态
    if (this.token) {
      if (this.isLoggedIn) {
        this.debounceGetHighlight();
      }
      // 延迟获取师傅信息，确保用户数据已经加载完成
      this.$nextTick(function () {
        _this3.fetchShifuInfo();
      });
    }
  },
  onShow: function onShow() {
    var _this4 = this;
    console.log('=== Mine页面onShow调试信息 ===');
    console.log('token:', this.token);
    console.log('isLoggedIn:', this.isLoggedIn);
    console.log('userInfo from Vuex:', this.userInfo);
    console.log('shiInfo from storage:', uni.getStorageSync('shiInfo'));
    console.log('displayUserInfo:', this.displayUserInfo);

    // 确保初始状态不是loading
    this.isLoading = false;

    // 检查是否有token（从 Vuex 和本地存储双重检查）
    var storageToken = uni.getStorageSync('token');
    var vuexToken = this.token;
    console.log('Token 检查:', {
      vuexToken: vuexToken,
      storageToken: storageToken,
      hasAnyToken: !!(vuexToken || storageToken)
    });

    // 如果没有任何token，直接清除登录状态
    if (!vuexToken && !storageToken) {
      console.log('没有任何token，清除登录状态');
      this.handleInvalidSession();
      return;
    }

    // 如果本地存储有token但Vuex没有，同步到Vuex
    if (storageToken && !vuexToken) {
      console.log('同步本地存储的token到Vuex');
      this.updateUserItem({
        key: 'autograph',
        val: storageToken
      });
    }

    // 每次显示页面时都先加载缓存的 labelName
    this.loadCachedLabelName();

    // 如果有token但用户信息不完整，尝试初始化用户数据
    var hasUserInfo = !!(this.userInfo.userId || this.userInfo.phone);
    if (this.isLoggedIn && !hasUserInfo) {
      console.log('有token但用户信息未完整加载，尝试初始化用户数据');
      this.initUserData();
      // 给一点时间让数据初始化完成，但不显示loading状态
      setTimeout(function () {
        var hasUserInfoAfterInit = !!(_this4.userInfo.userId || _this4.userInfo.phone);
        if (_this4.isLoggedIn && hasUserInfoAfterInit) {
          _this4.loadUserRelatedData();
        }
      }, 100);
    } else if (this.isLoggedIn && hasUserInfo) {
      // 已登录且有用户信息，直接加载用户相关数据
      this.loadUserRelatedData();
    }
    this.$nextTick(function () {
      _this4.$forceUpdate();
    });
  },
  onPullDownRefresh: function onPullDownRefresh() {
    // Handle pull-down refresh
    if (this.isLoggedIn && this.token) {
      Promise.all([this.fetchUserInfo(), this.getHighlight(), this.fetchShifuInfo(), this.getmyGrade() // 添加师傅等级刷新
      ]).then(function () {
        uni.stopPullDownRefresh();
        // this.showToast('刷新成功', 'success');
      }).catch(function (err) {
        console.error('Pull-down refresh failed:', err);
        uni.stopPullDownRefresh();
      });
    } else {
      // If not logged in, reset UI and stop refresh
      this.handleInvalidSession();
      uni.stopPullDownRefresh();
      this.showToast('请先登录');
    }
  },
  methods: _objectSpread(_objectSpread({
    // 从缓存加载 labelName，避免显示延迟
    loadCachedLabelName: function loadCachedLabelName() {
      var cachedLabelName = uni.getStorageSync('labelName');
      if (cachedLabelName) {
        this.labelName = cachedLabelName;
        console.log('从缓存加载 labelName:', cachedLabelName);
      }
    },
    getmyGrade: function getmyGrade() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var cachedLabelName, res;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                // 先从缓存加载，确保立即显示
                cachedLabelName = uni.getStorageSync('labelName');
                if (cachedLabelName && !_this5.labelName) {
                  _this5.labelName = cachedLabelName;
                  console.log('立即显示缓存的 labelName:', cachedLabelName);
                }

                // 然后异步获取最新数据
                _context.next = 5;
                return _this5.$api.shifu.getGrade();
              case 5:
                res = _context.sent;
                console.log('getGrade response:', res);
                if (res && res.data && res.data.labelName) {
                  _this5.labelName = res.data.labelName;
                  // 缓存 labelName，下次可以立即显示
                  uni.setStorageSync('labelName', res.data.labelName);
                  console.log('更新并缓存 labelName:', res.data.labelName);

                  // 如果数据有更新，强制刷新界面
                  _this5.$forceUpdate();
                }
                return _context.abrupt("return", res);
              case 11:
                _context.prev = 11;
                _context.t0 = _context["catch"](0);
                console.error('获取师傅等级失败:', _context.t0);

                // 如果是401错误，清除登录状态
                if (!(_context.t0.status === 401 || _context.t0.data && _context.t0.data.code === 401)) {
                  _context.next = 18;
                  break;
                }
                console.log('师傅等级接口401错误，清除登录状态');
                _this5.handleInvalidSession();
                return _context.abrupt("return", null);
              case 18:
                // 如果接口失败，保持使用缓存的值
                if (!_this5.labelName) {
                  _this5.labelName = uni.getStorageSync('labelName') || '';
                }
                return _context.abrupt("return", null);
              case 20:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 11]]);
      }))();
    },
    getmylogin: function getmylogin() {
      var _this6 = this;
      uni.login({
        provider: 'weixin',
        success: function success(res) {
          if (res.code) {
            _this6.code = res.code;
            console.log('Initial wx.login code:', _this6.code);
          }
        }
      });
    },
    getNowPosition: function getNowPosition() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var locationData;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return _locationManager.default.getLocation({
                  forceUpdate: false,
                  silent: true
                });
              case 3:
                locationData = _context2.sent;
                if (locationData && locationData.regeocode) {
                  // 更新 Vuex store - 使用正确的 action
                  _this7.$store.dispatch('service/setRegeocode', {
                    regeocode: locationData.regeocode,
                    lat: locationData.lat,
                    lng: locationData.lng
                  });
                  console.log("定位获取成功:", locationData);
                }
                _context2.next = 10;
                break;
              case 7:
                _context2.prev = 7;
                _context2.t0 = _context2["catch"](0);
                console.error("获取定位失败:", _context2.t0);
                // 定位失败不影响页面功能
              case 10:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 7]]);
      }))();
    },
    getshifuinfo: function getshifuinfo() {
      var _this8 = this;
      var userId = this.userInfo.userId || uni.getStorageSync('userId');
      if (!userId) {
        console.log('No userId, skipping getshifuinfo');
        return Promise.resolve();
      }
      return this.$api.shifu.getshifstutas({
        userId: userId
      }).then(function (res) {
        console.log('getshifstutas response:', res);
        _this8.shifustatus = res.data !== undefined && res.data !== null ? Number(res.data) : -1;
        if (res.data === -1) {
          var _this8$regeocode, _this8$regeocode$rege, _this8$regeocode2, _this8$regeocode3;
          var userinster = {
            userId: _this8.userInfo.userId || uni.getStorageSync('userId'),
            mobile: _this8.userInfo.phone || uni.getStorageSync('phone'),
            address: ((_this8$regeocode = _this8.regeocode) === null || _this8$regeocode === void 0 ? void 0 : (_this8$regeocode$rege = _this8$regeocode.regeocode) === null || _this8$regeocode$rege === void 0 ? void 0 : _this8$regeocode$rege.formatted_address) || '',
            cityId: '',
            labelId: 25,
            lng: ((_this8$regeocode2 = _this8.regeocode) === null || _this8$regeocode2 === void 0 ? void 0 : _this8$regeocode2.lng) || uni.getStorageSync('lng') || 0,
            lat: ((_this8$regeocode3 = _this8.regeocode) === null || _this8$regeocode3 === void 0 ? void 0 : _this8$regeocode3.lat) || uni.getStorageSync('lat') || 0
          };
          console.log('Registering master with:', userinster);
          return _this8.$api.shifu.masterEnter(userinster).then(function (res) {
            if (res.code === "200") {
              console.log('Master registration successful');
              return _this8.$api.shifu.getMaster();
            } else {
              throw new Error('Master registration failed');
            }
          }).then(function (masterRess) {
            if (!masterRess || (0, _typeof2.default)(masterRess) !== 'object') {
              throw new Error('获取师傅信息失败');
            }
            var masterRes = masterRess.data;
            var userInfo = {
              phone: masterRes.mobile || _this8.userInfo.phone || uni.getStorageSync('phone') || '',
              avatarUrl: masterRes.avatarUrl || _this8.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
              nickName: masterRes.coachName || _this8.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
              userId: masterRes.id || _this8.userInfo.userId || uni.getStorageSync('userId') || '',
              pid: masterRes.pid || _this8.userInfo.pid || uni.getStorageSync('pid') || ''
            };
            uni.setStorageSync('shiInfo', JSON.stringify({
              mobile: userInfo.phone,
              avatarUrl: userInfo.avatarUrl,
              coachName: userInfo.nickName,
              userId: userInfo.userId,
              shifuId: masterRes.id || '',
              pid: userInfo.pid,
              status: _this8.shifustatus,
              messagePush: Number(masterRes.messagePush) || -1
            }));
            _this8.updateUserItem({
              key: 'userInfo',
              val: userInfo
            });
            _this8.saveUserInfoToStorage(userInfo);
            _this8.$nextTick(function () {
              _this8.$forceUpdate();
            });
          });
        } else {
          return _this8.$api.shifu.getMaster().then(function (masterRess) {
            if (!masterRess || (0, _typeof2.default)(masterRess) !== 'object') {
              throw new Error('获取师傅信息失败');
            }
            var masterRes = masterRess.data;
            var userInfo = {
              phone: masterRes.mobile || _this8.userInfo.phone || uni.getStorageSync('phone') || '',
              avatarUrl: masterRes.avatarUrl || _this8.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
              nickName: masterRes.coachName || _this8.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
              userId: masterRes.id || _this8.userInfo.userId || uni.getStorageSync('userId') || '',
              pid: masterRes.pid || _this8.userInfo.pid || uni.getStorageSync('pid') || ''
            };
            uni.setStorageSync('shiInfo', JSON.stringify({
              mobile: userInfo.phone,
              avatarUrl: userInfo.avatarUrl,
              coachName: userInfo.nickName,
              userId: userInfo.userId,
              shifuId: masterRes.id || '',
              pid: userInfo.pid,
              status: _this8.shifustatus,
              messagePush: Number(masterRes.messagePush) || -1
            }));
            _this8.updateUserItem({
              key: 'userInfo',
              val: userInfo
            });
            _this8.saveUserInfoToStorage(userInfo);
            _this8.$nextTick(function () {
              _this8.$forceUpdate();
            });
          });
        }
      }).catch(function (err) {
        console.error('getshifuinfo error:', err);
        _this8.shifustatus = -1;
        var defaultUserInfo = {
          phone: _this8.userInfo.phone || uni.getStorageSync('phone') || '',
          avatarUrl: _this8.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
          nickName: _this8.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
          userId: _this8.userInfo.userId || uni.getStorageSync('userId') || '',
          pid: _this8.userInfo.pid || uni.getStorageSync('pid') || ''
        };
        uni.setStorageSync('shiInfo', JSON.stringify({
          mobile: defaultUserInfo.phone,
          avatarUrl: defaultUserInfo.avatarUrl,
          coachName: defaultUserInfo.nickName,
          userId: defaultUserInfo.userId,
          shifuId: '',
          pid: defaultUserInfo.pid,
          status: -1,
          messagePush: -1
        }));
        _this8.updateUserItem({
          key: 'userInfo',
          val: defaultUserInfo
        });
        _this8.saveUserInfoToStorage(defaultUserInfo);
        _this8.$nextTick(function () {
          _this8.$forceUpdate();
        });
      });
    },
    debounceGetHighlight: debounce(function () {
      this.getHighlight();
    }, 300),
    getHighlight: function getHighlight() {
      var _this9 = this;
      var userId = this.userInfo.userId || uni.getStorageSync('userId');
      if (!userId || !this.token || !this.isLoggedIn) {
        console.log('No userId, token, or not logged in, skipping getHighlight');
        return Promise.resolve();
      }
      // 确保只有在已登录状态下才显示loading
      this.isLoading = true;
      return this.$api.service.getHighlight({
        userId: userId,
        role: 1
      }).then(function (res) {
        console.log('getHighlight response:', res);
        var updatedOrderList = _this9.orderList.map(function (item, index) {
          return _objectSpread(_objectSpread({}, item), {}, {
            count: index === 0 ? res && res.countOrder ? res.countOrder : 0 : index === 1 ? res && res.shiFuBaoJia ? res.shiFuBaoJia : 0 : index === 2 ? res && res.daiZhiFu ? res.daiZhiFu : 0 : index === 3 ? res && res.daiFuWu ? res.daiFuWu : 0 : index === 4 ? res && res.fuWuZhong ? res.fuWuZhong : 0 : index === 5 ? res && res.yiWanCheng ? res.yiWanCheng : 0 : 0
          });
        });
        _this9.$set(_this9, 'orderList', updatedOrderList);
      }).finally(function () {
        _this9.isLoading = false;
      });
    },
    handleContact: function handleContact(e) {
      console.log(e.detail.path);
      console.log(e.detail.query);
    },
    // APP端客服电话拨打功能
    callCustomerService: function callCustomerService() {
      var phoneNumber = '13966580997';
      uni.makePhoneCall({
        phoneNumber: phoneNumber,
        success: function success() {
          console.log('拨打客服电话成功');
        },
        fail: function fail(err) {
          console.error('拨打客服电话失败:', err);
          uni.showToast({
            title: '拨打失败，请稍后重试',
            icon: 'none'
          });
        }
      });
    }
  }, (0, _vuex.mapMutations)('user', ['updateUserItem'])), {}, (_objectSpread2 = {
    showLoginPopup: function showLoginPopup() {
      // 检查当前运行环境

      console.log('非APP端显示登录弹窗');
      this.loginPopupVisible = true;

      // 运行时检查作为备用方案
      var systemInfo = uni.getSystemInfoSync();
      console.log('当前平台:', systemInfo.platform, systemInfo.app);
    },
    hideLoginPopup: function hideLoginPopup() {
      this.loginPopupVisible = false;
      this.agreedToTerms = false;
    },
    toggleAgreement: function toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms;
    },
    navigateToAgreement: function navigateToAgreement(type) {
      var url = '../user/configuser';
      if (type === 'service') {
        url += '?type=service';
      } else if (type === 'privacy') {
        url += '?type=privacy';
      }
      uni.navigateTo({
        url: url
      });
    },
    initUserData: function initUserData() {
      var _this10 = this;
      console.log('=== 初始化用户数据 ===');
      console.log('当前token:', this.token);
      console.log('当前userInfo:', this.userInfo);
      if (this.token && !this.userInfo.userId && !this.userInfo.phone) {
        console.log('有token但Vuex中没有用户信息，从本地存储恢复');
        var userInfo = {
          phone: uni.getStorageSync('phone') || '',
          avatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
          nickName: uni.getStorageSync('nickName') || '微信用户',
          userId: uni.getStorageSync('userId') || '',
          pid: uni.getStorageSync('pid') || '',
          unionid: uni.getStorageSync('unionid') || ''
        };
        console.log('从本地存储获取的用户信息:', userInfo);

        // 只要有userId就保存用户信息（微信登录可能没有手机号）
        if (userInfo.userId) {
          console.log('保存用户信息到Vuex');
          this.updateUserItem({
            key: 'userInfo',
            val: userInfo
          });
          this.saveUserInfoToStorage(userInfo);
          console.log('用户数据初始化成功');
          this.$nextTick(function () {
            _this10.$forceUpdate();
          });
        } else {
          console.log('本地存储中没有userId，尝试从服务器获取用户信息');
          // 不立即清除登录状态，而是尝试从服务器获取用户信息
          this.fetchUserInfoFromServer();
        }
      } else if (this.token && (this.userInfo.userId || this.userInfo.phone)) {
        console.log('token和用户信息都存在，无需初始化');
      } else {
        console.log('没有token，跳过初始化');
      }
    },
    // 从服务器获取用户信息
    fetchUserInfoFromServer: function fetchUserInfoFromServer() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var response, userData, userInfoFormatted;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                console.log('=== 从服务器获取用户信息 ===');
                _context3.prev = 1;
                _context3.next = 4;
                return _this11.$api.user.userInfo();
              case 4:
                response = _context3.sent;
                if (response && response.data) {
                  userData = response.data;
                  console.log('从服务器获取的用户信息:', userData);
                  userInfoFormatted = {
                    phone: userData.phone || '',
                    avatarUrl: userData.avatarUrl || '/static/mine/default_user.png',
                    nickName: userData.nickName || '微信用户',
                    userId: userData.id || userData.userId || '',
                    createTime: userData.createTime || '',
                    pid: userData.pid || '',
                    inviteCode: userData.inviteCode || '',
                    unionid: userData.unionid || ''
                  };
                  if (userInfoFormatted.userId) {
                    console.log('服务器返回了有效的用户信息，保存到本地');
                    _this11.updateUserItem({
                      key: 'userInfo',
                      val: userInfoFormatted
                    });
                    _this11.saveUserInfoToStorage(userInfoFormatted);
                    _this11.$nextTick(function () {
                      _this11.$forceUpdate();
                    });
                  } else {
                    console.log('服务器返回的用户信息无效，清除登录状态');
                    _this11.handleInvalidSession();
                  }
                } else {
                  console.log('服务器未返回用户信息，清除登录状态');
                  _this11.handleInvalidSession();
                }
                _context3.next = 12;
                break;
              case 8:
                _context3.prev = 8;
                _context3.t0 = _context3["catch"](1);
                console.error('从服务器获取用户信息失败:', _context3.t0);
                // 如果是401错误（未授权），清除登录状态
                if (_context3.t0.status === 401 || _context3.t0.data && _context3.t0.data.code === 401) {
                  console.log('token已失效，清除登录状态');
                  _this11.handleInvalidSession();
                } else {
                  // 其他错误，暂时不清除登录状态，可能是网络问题
                  console.log('网络错误，暂时保持登录状态');
                }
              case 12:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[1, 8]]);
      }))();
    },
    // 加载用户相关数据的方法
    loadUserRelatedData: function loadUserRelatedData() {
      var _this12 = this;
      console.log('=== 开始加载用户相关数据 ===');
      if (!this.isLoggedIn || !this.token) {
        console.log('用户未登录，跳过数据加载');
        return;
      }

      // 并行加载多个数据，包括师傅等级信息
      var promises = [this.getHighlight() || Promise.resolve(), this.fetchShifuInfo() || Promise.resolve(), this.getmyGrade() || Promise.resolve()];
      Promise.all(promises.map(function (promise) {
        return Promise.resolve(promise).catch(function (err) {
          console.error('加载数据失败:', err);
          return null;
        });
      })).then(function () {
        console.log('用户相关数据加载完成');
        _this12.$nextTick(function () {
          _this12.$forceUpdate();
        });
      }).catch(function (err) {
        console.error('加载用户数据时发生错误:', err);
      });
    },
    // 获取师傅信息的方法（优化版本）
    fetchShifuInfo: function fetchShifuInfo() {
      console.log('=== 开始获取师傅信息 ===');
      var userId = this.userInfo.userId || uni.getStorageSync('userId');
      if (!userId) {
        console.log('没有userId，跳过师傅信息获取');
        return Promise.resolve();
      }

      // 先检查本地缓存，如果有最近的数据就直接使用
      var cachedShiInfo = uni.getStorageSync('shiInfo');
      var cacheTimestamp = uni.getStorageSync('shiInfoTimestamp');
      var now = Date.now();
      var cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

      if (cachedShiInfo && cacheTimestamp && now - cacheTimestamp < cacheExpiry) {
        try {
          var parsedShiInfo = JSON.parse(cachedShiInfo);
          this.shifustatus = parsedShiInfo.status || -1;
          console.log('使用缓存的师傅信息:', parsedShiInfo.status);
          return Promise.resolve();
        } catch (error) {
          console.error('解析缓存的师傅信息失败:', error);
        }
      }

      // 缓存过期或不存在，重新获取
      return this.getshifuinfo().then(function () {
        // 更新缓存时间戳
        uni.setStorageSync('shiInfoTimestamp', now);
        console.log('师傅信息获取完成并更新缓存');
      }).catch(function (err) {
        console.error('获取师傅信息失败:', err);
        // 即使失败也不影响页面显示
      });
    },
    // 立即获取师傅信息的方法（用于解决未知状态问题）
    fetchShifuInfoImmediately: function fetchShifuInfoImmediately() {
      var _this13 = this;
      console.log('=== 立即获取师傅信息 ===');
      var userId = this.userInfo.userId || uni.getStorageSync('userId');
      if (!userId) {
        console.log('没有userId，无法立即获取师傅信息');
        return Promise.resolve();
      }

      // 强制重新获取，不使用缓存
      return this.getshifuinfo().then(function () {
        // 更新缓存时间戳
        uni.setStorageSync('shiInfoTimestamp', Date.now());
        console.log('立即获取师傅信息完成，状态:', _this13.shifustatus);
        // 强制更新界面
        _this13.$forceUpdate();
      }).catch(function (err) {
        console.error('立即获取师傅信息失败:', err);
        // 设置默认状态
        _this13.shifustatus = -1;
        _this13.$forceUpdate();
      });
    },
    // 添加缺失的 handleNavigate 方法
    handleNavigate: function handleNavigate(url) {
      var _this14 = this;
      if (!url) return;
      console.log('handleNavigate called with url:', url);

      // 检查是否需要登录
      var requiresLogin = ['/shifu/skills', '/shifu/Professiona', '/shifu/coreWallet', '/user/promotion', '/shifu/income',
      // orderList3 中的服务收入
      '/shifu/master_bao_list',
      // orderList3 中的报价列表
      '/shifu/master_my_order', '/shifu/Margin',
      // orderList3 中的保证金
      '/shifu/shifuGrade',
      // orderList3 中的师傅等级
      '/shifu/Settle',
      // toolList2 中的师傅入驻
      '/shifu/master_Info' // toolList2 中的编辑师傅资料
      ];

      if (requiresLogin.some(function (path) {
        return url.startsWith(path);
      }) && !this.isLoggedIn) {
        return this.showLoginPopup();
      }
      uni.navigateTo({
        url: url,
        fail: function fail(err) {
          console.error('页面跳转失败:', err);
          _this14.showToast('页面跳转失败，请重试');
        }
      });
    },
    navigateTo: function navigateTo(url) {
      if (!url) return;
      var requiresLogin = ['/shifu/master_my_order',
      // orderList 中的所有订单页面
      '../user/userProfile' // 用户资料页面
      // '../user/coupon', // These were commented out in the original code
      // '../user/repair_record',
      // '../user/order_list',
      // '../user/address',
      // '../user/Settle',
      // '../user/agent_apply',
      // '../user/promotion',
      // '../user/bankCard',
      // '../shifu/Settle',
      // '../shifu/Receiving',
      // '../shifu/mine'
      ];

      if (requiresLogin.some(function (path) {
        return url.startsWith(path);
      }) && !this.isLoggedIn) {
        return this.showLoginPopup();
      }
      uni.navigateTo({
        url: url
      });
    },
    // 添加缺失的 showToast 方法
    showToast: function showToast(message) {
      var icon = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
      uni.showToast({
        title: message,
        icon: icon,
        duration: 2000
      });
    },
    // 添加缺失的手机号验证方法
    validatePhone: function validatePhone(phone) {
      var phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },
    // 添加微信登录获取手机号方法
    onGetPhoneNumber: function onGetPhoneNumber(e) {
      console.log('获取手机号:', e);
      if (!this.agreedToTerms) {
        this.showToast('请先同意服务协议和隐私政策');
        return;
      }
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        this.isLoading = true;
        // 处理微信登录逻辑
        this.handleWechatLogin(e.detail);
      } else {
        this.showToast('获取手机号失败，请重试');
      }
    },
    // 处理微信登录
    handleWechatLogin: function handleWechatLogin(phoneDetail) {
      var _this15 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var loginData, response, userInfo;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                // 这里应该调用你的登录API
                loginData = {
                  code: _this15.code,
                  encryptedData: phoneDetail.encryptedData,
                  iv: phoneDetail.iv,
                  inviteCode: _this15.inviteCode || ''
                };
                _context4.next = 4;
                return _this15.$api.user.wxLogin(loginData);
              case 4:
                response = _context4.sent;
                if (!(response && response.code === '200')) {
                  _context4.next = 14;
                  break;
                }
                // 登录成功，保存用户信息
                userInfo = response.data;
                _this15.updateUserItem({
                  key: 'autograph',
                  val: userInfo.token || ''
                });
                _this15.updateUserItem({
                  key: 'userInfo',
                  val: {
                    phone: userInfo.phone || '',
                    avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
                    nickName: userInfo.nickName || '微信用户',
                    userId: userInfo.userId || userInfo.id || '',
                    pid: userInfo.pid || ''
                  }
                });
                _this15.hideLoginPopup();
                _this15.showToast('登录成功', 'success');

                // 登录成功后加载用户相关数据
                _this15.loadUserRelatedData();
                _context4.next = 15;
                break;
              case 14:
                throw new Error(response.msg || '登录失败');
              case 15:
                _context4.next = 21;
                break;
              case 17:
                _context4.prev = 17;
                _context4.t0 = _context4["catch"](0);
                console.error('微信登录失败:', _context4.t0);
                _this15.showToast(_context4.t0.message || '登录失败，请重试');
              case 21:
                _context4.prev = 21;
                _this15.isLoading = false;
                return _context4.finish(21);
              case 24:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 17, 21, 24]]);
      }))();
    },
    // 显示绑定手机号弹窗
    showBindPhonePopup: function showBindPhonePopup() {
      this.bindPhonePopupVisible = true;
    },
    // 隐藏绑定手机号弹窗
    hideBindPhonePopup: function hideBindPhonePopup() {
      this.bindPhonePopupVisible = false;
      this.bindPhoneForm = {
        phone: '',
        code: ''
      };
    },
    // 发送绑定手机号验证码
    sendBindPhoneSmsCode: function sendBindPhoneSmsCode() {
      var _this16 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                if (_this16.validatePhone(_this16.bindPhoneForm.phone)) {
                  _context5.next = 3;
                  break;
                }
                _this16.showToast('请输入正确的手机号');
                return _context5.abrupt("return");
              case 3:
                if (!(_this16.bindPhoneSmsCountdown > 0)) {
                  _context5.next = 5;
                  break;
                }
                return _context5.abrupt("return");
              case 5:
                _context5.prev = 5;
                _context5.next = 8;
                return _this16.$api.user.sendSmsCode({
                  phone: _this16.bindPhoneForm.phone,
                  type: 'bind'
                });
              case 8:
                _this16.showToast('验证码已发送', 'success');
                _this16.startBindPhoneSmsCountdown();
                _context5.next = 16;
                break;
              case 12:
                _context5.prev = 12;
                _context5.t0 = _context5["catch"](5);
                console.error('发送验证码失败:', _context5.t0);
                _this16.showToast(_context5.t0.message || '发送验证码失败');
              case 16:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[5, 12]]);
      }))();
    },
    // 开始验证码倒计时
    startBindPhoneSmsCountdown: function startBindPhoneSmsCountdown() {
      var _this17 = this;
      this.bindPhoneSmsCountdown = 60;
      this.bindPhoneSmsTimer = setInterval(function () {
        _this17.bindPhoneSmsCountdown--;
        if (_this17.bindPhoneSmsCountdown <= 0) {
          clearInterval(_this17.bindPhoneSmsTimer);
          _this17.bindPhoneSmsTimer = null;
        }
      }, 1000);
    },
    saveUserInfoToStorage: function saveUserInfoToStorage(userInfo) {
      uni.setStorageSync('phone', userInfo.phone || '');
      uni.setStorageSync('avatarUrl', userInfo.avatarUrl || '');
      uni.setStorageSync('nickName', userInfo.nickName || '');
      uni.setStorageSync('userId', userInfo.userId || '');
      uni.setStorageSync('pid', userInfo.pid || '');
      if (userInfo.unionid) {
        uni.setStorageSync('unionid', userInfo.unionid);
      }
    },
    fetchUserInfo: function fetchUserInfo() {
      var _this18 = this;
      if (this.isLoading || !this.token) {
        console.log('Skipping fetchUserInfo: no token or already loading');
        return Promise.resolve();
      }
      this.isLoading = true;
      return this.$api.user.userInfo().then(function (responses) {
        var response = responses.data;
        if (!response || (0, _typeof2.default)(response) !== 'object') {
          throw new Error('获取用户信息失败: 响应数据无效');
        }
        var userInfo = {
          phone: response.data.phone || '',
          avatarUrl: response.data.avatarUrl || '/static/mine/default_user.png',
          nickName: response.data.nickName || '微信用户',
          userId: response.data.id || '',
          createTime: response.data.createTime || '',
          pid: response.data.pid || '',
          inviteCode: response.data.inviteCode || ''
        };
        _this18.updateUserItem({
          key: 'userInfo',
          val: userInfo
        });
        console.log(userInfo);
        _this18.saveUserInfoToStorage(userInfo);
      }).catch(function (error) {
        console.error('获取用户信息失败:', error);
        if (error.message && error.message.includes('响应数据无效')) {
          _this18.handleInvalidSession();
        } else {
          if (_this18.token) {
            // this.showToast('获取用户信息失败，请稍后重试');
          }
        }
      }).finally(function () {
        _this18.isLoading = false;
      });
    },
    handleInvalidSession: function handleInvalidSession() {
      var _this19 = this;
      console.log('=== 清除登录状态 ===');

      // 清除所有用户相关的本地存储数据
      var keysToRemove = ['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'shiInfo', 'shiInfoTimestamp', 'labelName', 'unionid', 'appOpenid', 'receivedInviteCode', 'userInfo'];
      keysToRemove.forEach(function (key) {
        uni.removeStorageSync(key);
        console.log("\u5DF2\u6E05\u9664\u672C\u5730\u5B58\u50A8: ".concat(key));
      });

      // 清除 Vuex 中的用户数据
      this.updateUserItem({
        key: 'userInfo',
        val: {}
      });
      this.updateUserItem({
        key: 'autograph',
        val: ''
      });

      // 重置页面状态
      this.isLoading = false;
      this.shifustatus = -1;
      this.labelName = '';

      // 重置订单列表计数
      this.$set(this, 'orderList', this.orderList.map(function (item) {
        return _objectSpread(_objectSpread({}, item), {}, {
          count: 0
        });
      }));
      console.log('登录状态清除完成');

      // 强制更新界面
      this.$nextTick(function () {
        _this19.$forceUpdate();
      });
    }
  }, (0, _defineProperty2.default)(_objectSpread2, "showBindPhonePopup", function showBindPhonePopup() {
    this.bindPhonePopupVisible = true;
  }), (0, _defineProperty2.default)(_objectSpread2, "hideBindPhonePopup", function hideBindPhonePopup() {
    this.bindPhonePopupVisible = false;
    this.bindPhoneForm = {
      phone: '',
      code: ''
    };
    if (this.bindPhoneSmsTimer) {
      clearInterval(this.bindPhoneSmsTimer);
      this.bindPhoneSmsTimer = null;
      this.bindPhoneSmsCountdown = 0;
    }
  }), (0, _defineProperty2.default)(_objectSpread2, "validatePhone", function validatePhone(phone) {
    var phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  }), (0, _defineProperty2.default)(_objectSpread2, "sendBindPhoneSmsCode", function sendBindPhoneSmsCode() {
    var _this20 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
      var phone, response;
      return _regenerator.default.wrap(function _callee6$(_context6) {
        while (1) {
          switch (_context6.prev = _context6.next) {
            case 0:
              if (!(_this20.bindPhoneSmsCountdown > 0)) {
                _context6.next = 2;
                break;
              }
              return _context6.abrupt("return");
            case 2:
              phone = _this20.bindPhoneForm.phone;
              if (_this20.validatePhone(phone)) {
                _context6.next = 5;
                break;
              }
              return _context6.abrupt("return", _this20.showToast('请输入正确的手机号'));
            case 5:
              _context6.prev = 5;
              _context6.next = 8;
              return _this20.$api.base.sendSmsCode({
                phone: phone
              });
            case 8:
              response = _context6.sent;
              if (response.code === '200') {
                _this20.showToast('验证码发送成功', 'success');
                _this20.startBindPhoneCountdown();
              } else {
                _this20.showToast(response.msg || '验证码发送失败，请重试');
              }
              _context6.next = 16;
              break;
            case 12:
              _context6.prev = 12;
              _context6.t0 = _context6["catch"](5);
              console.error('发送验证码失败:', _context6.t0);
              _this20.showToast('验证码发送失败，请重试');
            case 16:
            case "end":
              return _context6.stop();
          }
        }
      }, _callee6, null, [[5, 12]]);
    }))();
  }), (0, _defineProperty2.default)(_objectSpread2, "startBindPhoneCountdown", function startBindPhoneCountdown() {
    var _this21 = this;
    this.bindPhoneSmsCountdown = 60;
    this.bindPhoneSmsTimer = setInterval(function () {
      _this21.bindPhoneSmsCountdown--;
      if (_this21.bindPhoneSmsCountdown <= 0) {
        clearInterval(_this21.bindPhoneSmsTimer);
        _this21.bindPhoneSmsTimer = null;
      }
    }, 1000);
  }), (0, _defineProperty2.default)(_objectSpread2, "handleBindPhone", function handleBindPhone() {
    var _this22 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
      var _this22$bindPhoneForm, phone, code, unionid, registerID, params, response, updatedUserInfo, shiInfo;
      return _regenerator.default.wrap(function _callee7$(_context7) {
        while (1) {
          switch (_context7.prev = _context7.next) {
            case 0:
              if (!(!_this22.canBindPhone || _this22.isBindingPhone)) {
                _context7.next = 2;
                break;
              }
              return _context7.abrupt("return");
            case 2:
              _this22$bindPhoneForm = _this22.bindPhoneForm, phone = _this22$bindPhoneForm.phone, code = _this22$bindPhoneForm.code;
              if (_this22.validatePhone(phone)) {
                _context7.next = 5;
                break;
              }
              return _context7.abrupt("return", _this22.showToast('请输入正确的手机号'));
            case 5:
              if (code) {
                _context7.next = 7;
                break;
              }
              return _context7.abrupt("return", _this22.showToast('请输入验证码'));
            case 7:
              _this22.isBindingPhone = true;
              uni.showLoading({
                title: '绑定中...'
              });
              _context7.prev = 9;
              // 获取unionid
              unionid = uni.getStorageSync('unionid');
              if (unionid) {
                _context7.next = 13;
                break;
              }
              throw new Error('缺少微信用户标识，请重新登录');
            case 13:
              registerID = uni.getStorageSync("registerID"); // 调用绑定接口
              params = {
                phone: phone,
                shortCode: code,
                unionid: unionid,
                platform: 1,
                // 师傅端
                registrationId: registerID || 'xxx' // 极光推送id
              };

              console.log('绑定手机号参数:', params);
              _context7.next = 18;
              return _this22.$api.user.register(params);
            case 18:
              response = _context7.sent;
              console.log('绑定手机号响应:', response);
              if (!(response.code === '200')) {
                _context7.next = 32;
                break;
              }
              _this22.showToast('绑定成功', 'success');

              // 更新用户信息
              updatedUserInfo = _objectSpread(_objectSpread({}, _this22.userInfo), {}, {
                phone: phone
              });
              _this22.updateUserItem({
                key: 'userInfo',
                val: updatedUserInfo
              });

              // 更新本地存储
              uni.setStorageSync('phone', phone);

              // 更新师傅信息存储
              shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
              shiInfo.mobile = phone;
              uni.setStorageSync('shiInfo', JSON.stringify(shiInfo));

              // 关闭弹窗
              _this22.hideBindPhonePopup();

              // 刷新用户信息
              _this22.fetchUserInfo();
              _context7.next = 33;
              break;
            case 32:
              throw new Error(response.msg || '绑定失败，请重试');
            case 33:
              _context7.next = 39;
              break;
            case 35:
              _context7.prev = 35;
              _context7.t0 = _context7["catch"](9);
              console.error('绑定手机号失败:', _context7.t0);
              _this22.showToast(_context7.t0.message || '绑定失败，请重试');
            case 39:
              _context7.prev = 39;
              _this22.isBindingPhone = false;
              uni.hideLoading();
              return _context7.finish(39);
            case 43:
            case "end":
              return _context7.stop();
          }
        }
      }, _callee7, null, [[9, 35, 39, 43]]);
    }))();
  }), (0, _defineProperty2.default)(_objectSpread2, "onGetPhoneNumber", function onGetPhoneNumber(e) {
    var _this23 = this;
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      return this.showToast('授权失败，请重试');
    }
    this.getmylogin();
    this.isLoading = true;
    uni.showLoading({
      mask: true,
      title: '登录中...'
    });
    var _e$detail = e.detail,
      encryptedData = _e$detail.encryptedData,
      iv = _e$detail.iv;
    uni.checkSession({
      success: function success() {
        _this23.loginWithWeixin({
          code: _this23.code,
          encryptedData: encryptedData,
          iv: iv,
          platform: 1,
          pid: _this23.inviteCode
        });
      },
      fail: function fail() {
        uni.login({
          provider: 'weixin',
          success: function success(res) {
            if (res.code) {
              _this23.code = res.code;
              console.log('Refreshed wx.login code:', _this23.code);
              _this23.loginWithWeixin({
                code: _this23.code,
                encryptedData: encryptedData,
                iv: iv,
                platform: 1,
                pid: _this23.inviteCode
              });
            } else {
              _this23.isLoading = false;
              uni.hideLoading();
              _this23.showToast('获取登录凭证失败');
            }
          },
          fail: function fail() {
            _this23.isLoading = false;
            uni.hideLoading();
            _this23.showToast('微信登录失败，请重试');
          }
        });
      }
    });
  }), (0, _defineProperty2.default)(_objectSpread2, "loginWithWeixin", function loginWithWeixin(params) {
    var _this24 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
      var response, userInfoResponse, userInfoRes, initialUserInfo, masterRes, masterData, userInfo, shiInfoData, modalShownKey, hasShownModal;
      return _regenerator.default.wrap(function _callee8$(_context8) {
        while (1) {
          switch (_context8.prev = _context8.next) {
            case 0:
              _context8.prev = 0;
              _context8.next = 3;
              return _this24.$api.user.loginuserInfo({
                code: params.code,
                encryptedData: params.encryptedData,
                iv: params.iv,
                platform: 1,
                pid: _this24.inviteCode
              });
            case 3:
              response = _context8.sent;
              console.log('登录响应:', response);
              if (!(!response || !response.data.token)) {
                _context8.next = 7;
                break;
              }
              throw new Error('请重新登录');
            case 7:
              // 确保token被正确保存到所有地方
              uni.setStorageSync('token', response.data.token);

              // 使用 await 确保 Vuex 更新完成
              _context8.next = 10;
              return _this24.$nextTick();
            case 10:
              _this24.updateUserItem({
                key: 'autograph',
                val: response.data.token
              });

              // 再次等待确保状态更新完成
              _context8.next = 13;
              return _this24.$nextTick();
            case 13:
              console.log('Token saved:', response.data.token);
              console.log('Token from Vuex:', _this24.$store.state.user.autograph);
              console.log('Token from storage:', uni.getStorageSync('token'));

              // 返回 userInfo 请求，确保 token 已设置
              _context8.next = 18;
              return _this24.$api.user.userInfo();
            case 18:
              userInfoResponse = _context8.sent;
              userInfoRes = userInfoResponse.data;
              if (!(!userInfoRes || (0, _typeof2.default)(userInfoRes) !== 'object')) {
                _context8.next = 23;
                break;
              }
              console.error('获取用户信息失败 - 响应数据:', userInfoRes);
              throw new Error('获取用户信息失败');
            case 23:
              console.log('用户信息获取成功:', userInfoRes);
              initialUserInfo = {
                phone: userInfoRes.phone || '',
                avatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',
                nickName: userInfoRes.nickName || '微信用户',
                userId: userInfoRes.id || '',
                pid: userInfoRes.pid || ''
              };
              _this24.updateUserItem({
                key: 'userInfo',
                val: initialUserInfo
              });
              _this24.saveUserInfoToStorage(initialUserInfo);

              // 直接获取师傅信息
              _context8.next = 29;
              return _this24.$api.shifu.getMaster();
            case 29:
              masterRes = _context8.sent;
              console.log('getMaster响应:', masterRes);
              if (!(!masterRes || (0, _typeof2.default)(masterRes) !== 'object')) {
                _context8.next = 33;
                break;
              }
              throw new Error('获取师傅信息失败');
            case 33:
              masterData = masterRes.data; // 如果getMaster返回了状态信息，优先使用
              if (masterData && masterData.status !== undefined) {
                _this24.shifustatus = Number(masterData.status);
                console.log('从getMaster获取的状态:', _this24.shifustatus);
              } else {
                // 如果getMaster没有返回状态，使用之前获取的状态
                console.log('getMaster未返回状态，使用当前状态:', _this24.shifustatus);
              }
              userInfo = {
                phone: masterData.mobile || _this24.userInfo.phone || uni.getStorageSync('phone') || '',
                avatarUrl: masterData.avatarUrl || _this24.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
                nickName: masterData.coachName || _this24.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
                userId: masterData.userId || _this24.userInfo.userId || uni.getStorageSync('userId') || '',
                shifuId: masterData.id || '',
                pid: masterData.pid || _this24.userInfo.pid || uni.getStorageSync('pid') || ''
              }; // 保存师傅信息到本地存储
              shiInfoData = {
                mobile: userInfo.phone,
                avatarUrl: userInfo.avatarUrl,
                coachName: userInfo.nickName,
                shifuId: userInfo.shifuId,
                userId: userInfo.userId,
                pid: userInfo.pid,
                status: _this24.shifustatus,
                messagePush: Number(masterData.messagePush) || -1
              };
              uni.setStorageSync('shiInfo', JSON.stringify(shiInfoData));
              // 立即更新缓存时间戳，确保状态能立即显示
              uni.setStorageSync('shiInfoTimestamp', Date.now());
              console.log('登录完成，保存师傅信息:', shiInfoData);
              console.log('最终师傅状态:', _this24.shifustatus);
              _this24.updateUserItem({
                key: 'userInfo',
                val: userInfo
              });
              _this24.saveUserInfoToStorage(userInfo);

              // 异步获取师傅等级信息，确保 labelName 能正确显示
              // 由于 getMaster API 不包含 labelName，需要单独调用 getGrade
              _this24.getmyGrade();

              // 立即触发界面更新
              _this24.$forceUpdate();
              modalShownKey = "certificationModalShown_".concat(userInfo.userId, "_").concat(_this24.shifustatus);
              hasShownModal = uni.getStorageSync(modalShownKey);
              if (!hasShownModal && (_this24.shifustatus === -1 || _this24.shifustatus === 4) && _this24.isLoggedIn) {
                _this24.showCertificationPopup();
                uni.setStorageSync(modalShownKey, 'true');
              }
              _this24.showToast('登录成功', 'success');
              _this24.hideLoginPopup();
              _this24.debounceGetHighlight();

              // 强制更新界面，确保状态文本立即显示
              _this24.$nextTick(function () {
                // 确保状态文本能立即更新
                _this24.$forceUpdate();
                // 再次确认状态已正确设置
                console.log('登录完成后的最终状态检查:', {
                  shifustatus: _this24.shifustatus,
                  statusText: _this24.statusText,
                  labelName: _this24.labelName,
                  cachedShiInfo: uni.getStorageSync('shiInfo'),
                  isLoggedIn: _this24.isLoggedIn,
                  displayUserInfo: _this24.displayUserInfo
                });

                // 额外的强制更新，确保界面响应
                setTimeout(function () {
                  _this24.$forceUpdate();
                  console.log('延迟强制更新完成');
                }, 100);
              });
              _context8.next = 59;
              break;
            case 54:
              _context8.prev = 54;
              _context8.t0 = _context8["catch"](0);
              console.error('Login error:', _context8.t0);
              _this24.showToast(_context8.t0.message || '登录失败，请稍后重试');
              _this24.handleInvalidSession();
            case 59:
              _context8.prev = 59;
              _this24.isLoading = false;
              uni.hideLoading();
              return _context8.finish(59);
            case 63:
            case "end":
              return _context8.stop();
          }
        }
      }, _callee8, null, [[0, 54, 59, 63]]);
    }))();
  }), (0, _defineProperty2.default)(_objectSpread2, "showToast", function showToast(title) {
    var icon = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
    uni.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  }), (0, _defineProperty2.default)(_objectSpread2, "fetchShifuInfo", function fetchShifuInfo() {
    var _this25 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
      var shiInfoResponses, shiInfoResponse, userInfo, modalShownKey, hasShownModal, defaultUserInfo, _modalShownKey, _hasShownModal;
      return _regenerator.default.wrap(function _callee9$(_context9) {
        while (1) {
          switch (_context9.prev = _context9.next) {
            case 0:
              _context9.prev = 0;
              _this25.isLoading = true;
              _context9.next = 4;
              return _this25.$api.shifu.getMaster();
            case 4:
              shiInfoResponses = _context9.sent;
              shiInfoResponse = shiInfoResponses.data;
              console.log('fetchShifuInfo response:', shiInfoResponse);
              if (!(!shiInfoResponses || (0, _typeof2.default)(shiInfoResponses) !== 'object')) {
                _context9.next = 9;
                break;
              }
              throw new Error('获取师傅状态失败: 响应数据无效');
            case 9:
              _this25.shifustatus = shiInfoResponse.status !== undefined && shiInfoResponse.status !== null ? Number(shiInfoResponse.status) : -1;
              userInfo = {
                phone: shiInfoResponse.mobile || _this25.userInfo.phone || uni.getStorageSync('phone') || '',
                avatarUrl: shiInfoResponse.avatarUrl || _this25.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
                nickName: shiInfoResponse.coachName || _this25.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
                shifuId: shiInfoResponse.id || _this25.userInfo.userId || uni.getStorageSync('userId') || '',
                userId: shiInfoResponse.userId || _this25.userInfo.userId || uni.getStorageSync('userId') || '',
                pid: shiInfoResponse.pid || _this25.userInfo.pid || uni.getStorageSync('pid') || ''
              };
              uni.setStorageSync('shiInfo', JSON.stringify({
                mobile: userInfo.phone,
                avatarUrl: userInfo.avatarUrl,
                coachName: userInfo.nickName,
                userId: userInfo.userId,
                shifuId: userInfo.shifuId,
                pid: userInfo.pid,
                status: _this25.shifustatus,
                messagePush: Number(shiInfoResponse.messagePush) || -1
              }));
              _this25.updateUserItem({
                key: 'userInfo',
                val: userInfo
              });
              modalShownKey = "certificationModalShown_".concat(userInfo.userId, "_").concat(_this25.shifustatus);
              hasShownModal = uni.getStorageSync(modalShownKey);
              if (!hasShownModal && (_this25.shifustatus === -1 || _this25.shifustatus === 4) && _this25.isLoggedIn) {
                _this25.showCertificationPopup();
                uni.setStorageSync(modalShownKey, 'true');
              }
              _this25.$nextTick(function () {
                _this25.$forceUpdate();
              });
              _context9.next = 30;
              break;
            case 19:
              _context9.prev = 19;
              _context9.t0 = _context9["catch"](0);
              console.error('fetchShifuInfo error:', _context9.t0);
              _this25.shifustatus = -1;
              defaultUserInfo = {
                phone: _this25.userInfo.phone || uni.getStorageSync('phone') || '',
                avatarUrl: _this25.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
                nickName: _this25.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
                userId: _this25.userInfo.userId || uni.getStorageSync('userId') || '',
                pid: _this25.userInfo.pid || uni.getStorageSync('pid') || ''
              };
              uni.setStorageSync('shiInfo', JSON.stringify({
                mobile: defaultUserInfo.phone,
                avatarUrl: defaultUserInfo.avatarUrl,
                coachName: defaultUserInfo.nickName,
                userId: defaultUserInfo.userId,
                shifuId: '',
                // 默认为空字符串
                pid: defaultUserInfo.pid,
                status: -1,
                messagePush: -1
              }));
              _this25.updateUserItem({
                key: 'userInfo',
                val: defaultUserInfo
              });
              _modalShownKey = "certificationModalShown_".concat(defaultUserInfo.userId, "_").concat(defaultUserInfo.status);
              _hasShownModal = uni.getStorageSync(_modalShownKey);
              if (!_hasShownModal && defaultUserInfo.status === -1 && _this25.isLoggedIn) {
                _this25.showCertificationPopup();
                uni.setStorageSync(_modalShownKey, 'true');
              }
              _this25.$nextTick(function () {
                _this25.$forceUpdate();
              });
            case 30:
              _context9.prev = 30;
              _this25.isLoading = false;
              return _context9.finish(30);
            case 33:
            case "end":
              return _context9.stop();
          }
        }
      }, _callee9, null, [[0, 19, 30, 33]]);
    }))();
  }), (0, _defineProperty2.default)(_objectSpread2, "getshifustatus", function getshifustatus() {
    var shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
    this.shifustatus = shiInfo.status;
    console.log('getshifustatus:', this.shifustatus);
  }), (0, _defineProperty2.default)(_objectSpread2, "showCertificationPopup", function showCertificationPopup() {
    console.log('showCertificationPopup called, current shifustatus:', this.shifustatus);
    if (this.shifustatus === -1 || this.shifustatus === 4) {
      uni.showModal({
        title: '提示',
        content: this.shifustatus === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',
        confirmText: '去认证',
        cancelText: '再想想',
        cancelable: true,
        success: function success(res) {
          if (res.confirm) {
            var targetUrl = '/shifu/Settle';
            uni.navigateTo({
              url: targetUrl,
              fail: function fail(err) {
                console.error('Navigation to certification failed:', err);
                uni.showToast({
                  title: '跳转认证页面失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: function fail(err) {
          console.error('Modal failed:', err);
        }
      });
    }
  }), (0, _defineProperty2.default)(_objectSpread2, "handleNavigate", function handleNavigate(url) {
    var directNavigatePaths = ['/shifu/Settle', '/user/promotion', '/shifu/master_Info'];
    if (directNavigatePaths.includes(url)) {
      this.navigateTo(url);
      return;
    }

    // 先检查是否已登录
    if (!this.isLoggedIn) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.showLoginPopup();
      return;
    }
    if (this.shifustatus === -1 || this.shifustatus === 4) {
      uni.showToast({
        title: '你还不是师傅',
        icon: 'none'
      });
      this.showCertificationPopup();
    } else if (this.shifustatus === 1) {
      uni.showToast({
        title: '师傅状态在审核中',
        icon: 'none'
      });
    } else if (this.shifustatus === 2) {
      this.navigateTo(url);
    } else {
      this.navigateTo(url); // Fallback to navigate if status is unexpected.
    }
  }), (0, _defineProperty2.default)(_objectSpread2, "handleCallKf", function handleCallKf() {
    // 先检查是否已登录
    if (!this.isLoggedIn) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.showLoginPopup();
      return;
    }
    if (this.shifustatus === -1 || this.shifustatus === 4) {
      uni.showToast({
        title: '你还不是师傅',
        icon: 'none'
      });
      this.showCertificationPopup();
    } else if (this.shifustatus === 1) {
      uni.showToast({
        title: '师傅状态在审核中',
        icon: 'none'
      });
    } else if (this.shifustatus === 2) {
      this.callkf();
    }
  }), (0, _defineProperty2.default)(_objectSpread2, "callkf", function callkf() {
    uni.showToast({
      title: '联系客服功能待实现',
      icon: 'none'
    });
  }), (0, _defineProperty2.default)(_objectSpread2, "showToast", function showToast(title) {
    var icon = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';
    uni.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  }), _objectSpread2))
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 222:
/*!*************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=style&index=0&lang=scss& ***!
  \*************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&lang=scss& */ 223);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mine_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 223:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?vue&type=style&index=0&lang=scss& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[216,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/mine.js.map