{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?aac6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?91f1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?4481", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?4ee3", "uni-app:///user/commodity_details.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?10ec", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/commodity_details.vue?2b0c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "ready", "showChoose", "content", "id", "getconfigs", "serviceId", "lng", "lat", "serviceDet", "agents", "evalist", "tmplIds", "num", "fu<PERSON><PERSON><PERSON>", "configInfos", "long", "computed", "hasValidAgents", "agent", "primaryColor", "subColor", "configInfo", "commonOptions", "userInfo", "userPageType", "mineInfo", "methods", "seeImg", "uni", "urls", "current", "phoneDLD", "phoneNumber", "copyAddress", "success", "title", "icon", "moreEva", "getevalist", "console", "res", "item", "getfuwu", "cityName", "latitude", "longitude", "pageNum", "pageSize", "goUrl", "url", "confirmType", "$store", "processRichTextImages", "processed", "previewEvaImage", "getDetails", "onLoad", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAg2B,CAAgB,g3BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuHp3B;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC,UACA,gDACA,+CACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;QAAA,OACAC;MAAA,EACA;IACA;EAAA,GACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IACAC;MACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAH;QACAI;MACA;IACA;IACAC;MACAL;QACA7B;QACAmC;UACAN;YACAO;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;QACAT;UACAQ;UACAD;QACA;QACA;MACA;MACA;MACA;IACA;IACAG;MAAA;MACA;QACAC;QACA;QACA;QACAC;UACA;UACA;YACAC;cAAA;YAAA;UACA;YACAA;UACA;;UAEA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;QACAxC;QACAyC;QACAC;MACA;QACAR;QACA;QACA;MACA;IACA;IACAS;MACApB;QACAqB;MACA;IACA;IACAC;MACA;MACAX;MACA;QACAX;UACAO;UACAjC;UACAgC;YACA;cACAN;gBACAqB;cACA;YACA;UACA;QACA;QACA;MACA;MACA;MACAE;MACAvB;QACAqB;MACA;IACA;IACAG;MACA;MACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA1B;UACAC;UACAC;QACA;MACA;IACA;IACAyB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAhB;kBACA,oDACA,oBACAC,IACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;EACAgB;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAjB;cACAA;cACA;cACA;gBACA;cACA;cACA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAkB;AACA;AAAA,2B;;;;;;;;;;;;;AC9TA;AAAA;AAAA;AAAA;AAAumD,CAAgB,2jDAAG,EAAC,C;;;;;;;;;;;ACA3nD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/commodity_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/commodity_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./commodity_details.vue?vue&type=template&id=4d2a26fa&scoped=true&\"\nvar renderjs\nimport script from \"./commodity_details.vue?vue&type=script&lang=js&\"\nexport * from \"./commodity_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./commodity_details.vue?vue&type=style&index=0&id=4d2a26fa&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4d2a26fa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/commodity_details.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./commodity_details.vue?vue&type=template&id=4d2a26fa&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uRate: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-rate/u-rate\" */ \"uview-ui/components/u-rate/u-rate.vue\"\n      )\n    },\n    uDivider: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-divider/u-divider\" */ \"uview-ui/components/u-divider/u-divider.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.ready\n    ? _vm.serviceDet.agents &&\n      _vm.serviceDet.agents.length > 0 &&\n      _vm.hasValidAgents\n    : null\n  var g1 = _vm.ready && g0 ? _vm.serviceDet.agents.length : null\n  var g2 = _vm.ready && g0 && g1 > 2 ? _vm.serviceDet.agents.slice(0, 2) : null\n  var l0 = _vm.ready\n    ? _vm.__map(_vm.evalist, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g3 = item.imgs && item.imgList && item.imgList.length > 0\n        return {\n          $orig: $orig,\n          g3: g3,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showChoose = false\n    }\n    _vm.e1 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.seeImg(item.img)\n    }\n    _vm.e2 = function ($event, item) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item\n      var _temp3, _temp4\n      return _vm.seeImg(item.sunCode)\n    }\n    _vm.e3 = function ($event, item) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        item = _temp6.item\n      var _temp5, _temp6\n      return _vm.copyAddress(item.address)\n    }\n    _vm.e4 = function ($event, item) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        item = _temp8.item\n      var _temp7, _temp8\n      return _vm.phoneDLD(item.tel)\n    }\n    _vm.e5 = function ($event) {\n      _vm.showChoose = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./commodity_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./commodity_details.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\" v-if=\"ready\">\n    <u-modal :show=\"showChoose\" :content=\"content\"></u-modal>\n    <view class=\"choose_box\" :style=\"showChoose ? '' : 'height:0'\">\n      <view class=\"head\">选择下单模式</view>\n      <view class=\"close\" @tap=\"showChoose = false\">\n        <image src=\"/static/images/9397.png\" mode=\"\"></image>\n      </view>\n      <view class=\"choose_item\" @tap=\"confirmType(1)\" v-if=\"serviceDet.service_price_type != 0\">\n        <image src=\"/static/images/9469.png\" mode=\"\"></image>\n        <view class=\"title\">比价模式</view>\n        <view class=\"ctx\">订单发布后师傅进行报价，您可以实时比价，并选择最优师傅进行服务</view>\n      </view>\n    </view>\n    <view class=\"header\">\n      <image :src=\"serviceDet.cover\" mode=\"\"></image>\n      <view class=\"Info\">\n        <view class=\"title\">\n          {{ serviceDet.title }}\n          <span\n            v-if=\"serviceDet.service_price_type != 0\"\n            style=\"margin-left: 10rpx; font-size: 26rpx; color: #999;\"\n          >\n            (报价{{ serviceDet.initPrice }}元起)\n          </span>\n        </view>\n        <view class=\"price\" v-if=\"serviceDet.service_price_type != 1\">￥{{ serviceDet.price }}</view>\n        <view class=\"num\">已服务{{ serviceDet.totalSale }}次</view>\n      </view>\n    </view>\n    <view class=\"fg\"></view>\n    <view class=\"site\">\n      <view class=\"top\">\n        <view class=\"left\">附近服务点</view>\n        <view\n          class=\"right\"\n          @tap=\"goUrl(`../user/agent?id=${id}`)\"\n          v-if=\"hasValidAgents\"\n        >\n          查看更多\n        </view>\n        <view v-else>\n          暂无服务点\n        </view>\n      </view>\n      <view v-if=\"serviceDet.agents && serviceDet.agents.length > 0 && hasValidAgents\" class=\"site_box\">\n        <view\n          class=\"site_item\"\n          v-for=\"(item, index) in (serviceDet.agents.length > 2 ? serviceDet.agents.slice(0, 2) : serviceDet.agents)\"\n          :key=\"index\"\n        >\n          <image :src=\"item.img\" mode=\"scaleToFill\" style=\"border-radius: 10rpx;\" @tap=\"seeImg(item.img)\"></image>\n          <view class=\"content\">\n            <view class=\"name\">{{ item.serviceCate }}</view>\n            <view style=\"display: flex; justify-content: flex-end;\">\n              <u-icon name=\"attach\" size=\"28\" @tap=\"seeImg(item.sunCode)\"></u-icon>\n            </view>\n            <view class=\"address\">\n              <view class=\"position\" @tap=\"copyAddress(item.address)\">\n                <text>{{ item.name }} | {{ item.address }}</text>\n                <u-icon name=\"arrow-right\" color=\"#333\" size=\"10\"></u-icon>\n              </view>\n              <u-icon\n                name=\"phone-fill\"\n                color=\"#599eff\"\n                size=\"28\"\n                @tap=\"phoneDLD(item.tel)\"\n              ></u-icon>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"eva\">\n      <view class=\"top\">\n        <view class=\"left\">服务评价（{{ long }}条）</view>\n        <view class=\"right\" @tap=\"moreEva\">查看更多</view>\n      </view>\n      <view class=\"eva_item\" v-for=\"(item, index) in evalist\" :key=\"index\">\n        <view style=\"display: flex; justify-content: space-between; align-items: center;\" class=\"\">\n          <view style=\"margin-top: 20rpx;\" class=\"\">\n            <view class=\"top\">\n              <image :src=\"item.avatarUrl || '/static/mine/default_user.png'\" mode=\"\" @tap=\"seeImg(item.avatarUrl || '/static/mine/default_user.png')\"></image>\n              <view class=\"name\">{{ item.nickName || '' }}</view>\n            </view>\n            <view class=\"ctx\">{{ item.text }}</view>\n            <view class=\"eva_images\" v-if=\"item.imgs && item.imgList && item.imgList.length > 0\">\n              <image\n                v-for=\"(img, imgIndex) in item.imgList\"\n                :key=\"imgIndex\"\n                :src=\"img\"\n                style=\"width: 120rpx; height: 120rpx; margin-right: 20rpx;\"\n                @tap=\"previewEvaImage(img, item.imgList)\"\n              ></image>\n            </view>\n          </view>\n          <view class=\"\">\n            <view class=\"\"><u-rate readonly count=\"5\" v-model=\"item.star\"></u-rate></view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"details\">\n      <u-divider text=\"详情\" textColor=\"#333\"></u-divider>\n      <view class=\"\">\n        <rich-text alt=\"\" class=\"ddd\" :nodes=\"serviceDet.introduce\"></rich-text>\n      </view>\n    </view>\n    <view class=\"fg\"></view>\n    <view style=\"padding: 30rpx;\" class=\"\">\n      <rich-text :nodes=\"getconfigs.userNotice\"></rich-text>\n    </view>\n    <view class=\"footer\">\n      <view class=\"righ\" @tap=\"showChoose = true\">立即下单</view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState } from 'vuex';\nimport $store from '@/store/index.js';\n\nexport default {\n  data() {\n    return {\n      ready: false,\n      showChoose: false,\n      content: '',\n      id: '',\n      getconfigs: '',\n      serviceId: '',\n      lng: 115.277,\n      lat: 33.038799,\n      serviceDet: {\n        agents: []\n      },\n      evalist: [],\n      tmplIds: [\n        ' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n        '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY',\n        'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n        'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n      ],\n      num: 2,\n      fuwuinfo: [],\n      configInfos: '',\n      long: 0,\n    };\n  },\n  computed: {\n    hasValidAgents() {\n      if (!this.serviceDet.agents || this.serviceDet.agents.length === 0) {\n        return false;\n      }\n      return this.serviceDet.agents.some(agent => \n        agent && (agent.name || agent.serviceCate || agent.address || agent.img)\n      );\n    },\n    ...mapState({\n      primaryColor: (state) => state.config.configInfo.primaryColor,\n      subColor: (state) => state.config.configInfo.subColor,\n      configInfo: (state) => state.config.configInfo,\n      commonOptions: (state) => state.user.commonOptions,\n      userInfo: (state) => state.user.userInfo,\n      userPageType: (state) => state.user.userPageType,\n      mineInfo: (state) => state.user.mineInfo,\n    })\n  },\n  methods: {\n    seeImg(url) {\n      if (url) {\n        uni.previewImage({\n          urls: [url],\n          current: url\n        });\n      }\n    },\n    phoneDLD(info) {\n      uni.makePhoneCall({\n        phoneNumber: info,\n      });\n    },\n    copyAddress(address) {\n      uni.setClipboardData({\n        data: address,\n        success: () => {\n          uni.showToast({\n            title: '地址已复制',\n            icon: 'success'\n          });\n        }\n      });\n    },\n    moreEva() {\n      if (this.num + 1 > this.long) {\n        uni.showToast({\n          icon: 'none',\n          title: '没有更多了',\n        });\n        return;\n      }\n      this.num += 1;\n      this.getevalist();\n    },\n    getevalist() {\n      this.$api.service.getServiceCate(this.id).then((res) => {\n        console.log('evalist response:', res);\n        this.long = res.list.length;\n        // 处理评价数据\n        res.list.forEach((item) => {\n          // 处理评价图片\n          if (item.imgs && typeof item.imgs === 'string') {\n            item.imgList = item.imgs.split(',').filter(img => img.trim() !== '');\n          } else {\n            item.imgList = [];\n          }\n          \n          // 确保avatarUrl是字符串格式\n          if (item.avatarUrl && Array.isArray(item.avatarUrl)) {\n            item.avatarUrl = item.avatarUrl[0] || '';\n          } else if (typeof item.avatarUrl !== 'string') {\n            item.avatarUrl = '';\n          }\n        });\n        this.evalist = res.list.slice(0, this.num);\n      });\n    },\n    getfuwu() {\n      this.$api.service.getagents({\n        cityName: \"阜阳市\",\n        latitude: this.lat,\n        longitude: this.lng,\n        serviceId: this.id,\n        pageNum: 1,\n        pageSize: 5\n      }).then((res) => {\n        console.log('fuwu response:', res);\n        this.serviceDet.agents = res.list && res.list.length > 0 ? res.list : [];\n        this.fuwuinfo = res.list || [];\n      });\n    },\n    goUrl(e) {\n      uni.navigateTo({\n        url: e,\n      });\n    },\n    confirmType(e) {\n      let token = uni.getStorageSync('token');\n      console.log(token);\n      if (!token) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录',\n          success: (res) => {\n            if (res.confirm) {\n              uni.redirectTo({\n                url: '/pages/mine',\n              });\n            }\n          },\n        });\n        return;\n      }\n      this.showChoose = false;\n      $store.commit('changeType', e);\n      uni.navigateTo({\n        url: `../user/price_parity?id=${this.serviceDet.id}&type=${e}`,\n      });\n    },\n    processRichTextImages(html) {\n      if (!html) return html;\n      return html.replace(/<img[^>]+>/gi, (match) => {\n        let processed = match.replace(/\\s(width|height)=\"[^\"]*\"/gi, '');\n        if (!processed.includes('style=')) {\n          processed = processed.replace('<img', '<img style=\"max-width:100%;height:auto;\"');\n        } else {\n          processed = processed.replace('style=\"', 'style=\"max-width:100%;height:auto;');\n        }\n        return processed;\n      });\n    },\n    previewEvaImage(url, urls) {\n      if (url && urls && urls.length > 0) {\n        uni.previewImage({\n          urls: urls,\n          current: url,\n        });\n      }\n    },\n    async getDetails(id) {\n      await this.$api.service.orderGoodInfo(id).then((res) => {\n        console.log('service details:', res);\n        this.serviceDet = { \n          ...this.serviceDet, \n          ...res\n        };\n        if (this.serviceDet.introduce) {\n          this.serviceDet.introduce = this.processRichTextImages(this.serviceDet.introduce);\n        }\n      });\n    },\n  },\n  async onLoad(options) {\n    console.log('userInfo:', this.$store.state.user.userInfo);\n    console.log('onLoad options:', options);\n    this.id = options.id;\n    this.$api.base.getConfig().then(res => {\n      this.getconfigs = res.data;\n    });\n    this.configInfos = uni.getStorageSync('configInfo');\n    this.lat = uni.getStorageSync('lat');\n    this.lng = uni.getStorageSync('lng');\n    await this.getDetails(this.id);\n    await this.getevalist();\n    await this.getfuwu();\n    this.ready = true;\n  },\n  onShow() {},\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  padding-bottom: 166rpx;\n\n  ::v-deep .u-popup__content {\n    display: none;\n  }\n\n  .fg {\n    height: 20rpx;\n    background: #f0f0f0;\n  }\n\n  .choose_box {\n    width: 750rpx;\n    height: 692rpx;\n    background: #ffffff;\n    border-radius: 20rpx 20rpx 0rpx 0rpx;\n    position: fixed;\n    bottom: 0;\n    z-index: 10076;\n    transition: all 0.5s;\n\n    .head {\n      margin-top: 40rpx;\n      text-align: center;\n      font-size: 32rpx;\n      font-weight: 500;\n      color: #171717;\n    }\n\n    .close {\n      position: absolute;\n      top: 44rpx;\n      right: 32rpx;\n\n      image {\n        width: 37rpx;\n        height: 37rpx;\n      }\n    }\n\n    .choose_item {\n      width: 686rpx;\n      height: 200rpx;\n      position: relative;\n      margin: 0 auto;\n      margin-top: 40rpx;\n\n      image {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        z-index: -1;\n      }\n\n      .title {\n        padding-top: 40rpx;\n        padding-left: 40rpx;\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #824109;\n      }\n\n      .ctx {\n        max-width: 524rpx;\n        margin-top: 16rpx;\n        padding-left: 40rpx;\n        font-size: 24rpx;\n        font-weight: 400;\n        color: #a38071;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        display: -webkit-box;\n        -webkit-line-clamp: 2;\n        -webkit-box-orient: vertical;\n        white-space: normal;\n      }\n    }\n  }\n\n  .header {\n    image {\n      width: 750rpx;\n      height: 620rpx;\n    }\n\n    .Info {\n      padding: 40rpx 32rpx;\n\n      .title {\n        max-width: 550rpx;\n        font-size: 40rpx;\n        font-weight: 500;\n        color: #171717;\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n      }\n\n      .price {\n        margin-top: 12rpx;\n        font-size: 30rpx;\n        font-weight: 500;\n        color: #e72427;\n      }\n\n      .num {\n        margin-top: 12rpx;\n        font-size: 24rpx;\n        fontgroups-weight: 400;\n        color: #999999;\n      }\n    }\n  }\n\n  .site {\n    padding: 40rpx 32rpx;\n\n    .top {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .left {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #171717;\n      }\n\n      .right {\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #2e80fe;\n      }\n    }\n\n    .site_box {\n      .site_item {\n        margin-top: 40rpx;\n        display: flex;\n        border-bottom: 2rpx solid #e9e9e9;\n\n        image {\n          width: 202rpx;\n          height: 142rpx;\n          margin-right: 20rpx;\n        }\n\n        .content {\n          flex: 1;\n\n          .name {\n            width: 464rpx;\n            min-height: 76rpx;\n            font-size: 28rpx;\n            color: #333333;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            white-space: normal;\n          }\n\n          .address {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .position {\n              display: flex;\n              align-items: center;\n\n              text {\n                font-size: 24rpx;\n                color: #333333;\n                max-width: 320rpx;\n                white-space: normal;\n                margin-right: 10rpx;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .details {\n    padding: 20rpx 30rpx;\n\n    .ddd {\n      width: 100%;\n      overflow: hidden;\n\n      img {\n        max-width: 100% !important;\n        height: auto !important;\n        display: block;\n      }\n    }\n  }\n\n  .eva {\n    padding: 20rpx 30rpx;\n\n    .top {\n      padding-bottom: 20rpx;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      border-bottom: 2rpx solid #dddddd;\n\n      .left {\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #3b3b3b;\n      }\n\n      .right {\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #999999;\n      }\n    }\n\n    .eva_item {\n      padding: 20rpx 0;\n      border-bottom: 2rpx solid #dddddd;\n\n      .top {\n        display: flex;\n        align-items: center;\n        border: none;\n        padding-bottom: 0;\n\n        image {\n          width: 60rpx;\n          height: 60rpx;\n          border-radius: 50%;\n          margin-right: 12rpx;\n          cursor: pointer;\n        }\n      }\n\n      .ctx {\n        margin-top: 18rpx;\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #999999;\n      }\n\n      .eva_images {\n        margin-top: 18rpx;\n        display: flex;\n        flex-wrap: wrap;\n        \n        image {\n          cursor: pointer;\n          border-radius: 8rpx;\n        }\n      }\n    }\n  }\n\n  .footer {\n    padding: 38rpx 32rpx;\n    width: 750rpx;\n    background: #ffffff;\n    box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\n    position: fixed;\n    bottom: 0;\n    display: flex;\n    align-items: center;\n\n    .righ {\n      width: 690rpx;\n      height: 88rpx;\n      background: #2e80fe;\n      border-radius: 44rpx;\n      font-size: 28rpx;\n      font-weight: 400;\n      color: #ffffff;\n      line-height: 88rpx;\n      text-align: center;\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./commodity_details.vue?vue&type=style&index=0&id=4d2a26fa&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./commodity_details.vue?vue&type=style&index=0&id=4d2a26fa&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755676977992\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}