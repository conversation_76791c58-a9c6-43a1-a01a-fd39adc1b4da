{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?b57e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?63f8", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?bf37", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?6840", "uni-app:///shifu/Professiona.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?1572", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Professiona.vue?4e70"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "menuItems", "name", "imgField", "showModal", "Info", "selectedItem", "form", "electricianImg", "driverLicenseImg", "workPermitImg", "maintenanceCertificateImg", "gkzyAImg", "gkzyBImg", "ksbaImg", "hgzImg", "rdAImg", "rdBImg", "tzhyhkzImg", "otherImg", "onLoad", "methods", "fetchMasterData", "res", "console", "formData", "icon", "title", "handleItemClick", "closeModal", "getImage", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "fail", "uploadImage", "filePath", "type", "response", "deleteImage", "deleteData", "confirmUpload", "currentImage", "updatedInfo", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4C92B;EACAC;IACA;MACAC,YACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACAC;;kBAEA;kBACA;;kBAEA;kBACAC;kBACA;oBACA,iFACAF,0BACA;oBACAE;kBACA;kBAEA;gBACA;kBACAD;kBACA;oBACAE;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACA;kBACAE;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACAC;UACAb;QACA;MACA;IACA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAX;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACAY;kBACArC;kBACAuB;oBACAe;kBACA;gBACA;cAAA;gBANAC;gBAAA,KAQAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACAT;kBACAL;kBACAD;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACAF;gBACAQ;kBACAL;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;kBAAAf;gBAAA;;gBAEA;gBACAgB,6CACA,oDACA,sCAGA;gBAAA;gBAAA,OACA;cAAA;gBAAApB;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;;gBAEA;gBACA;kBACAI;kBACAD;gBACA;;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;gBACA;gBACA;kBACA;kBACA;kBACA;kBACA;oBACAG;oBACAD;kBACA;gBACA;kBACA;oBACAA;oBACAC;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAnB;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAmB,8CACA,oDACA;gBAGA;gBAAA;gBAAA,OAEA;cAAA;gBAAAvB;gBACA;kBACA;oBACAI;oBACAD;oBACAqB;kBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;gBACA;kBACAE;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3QA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Professiona.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Professiona.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Professiona.vue?vue&type=template&id=47061b1b&scoped=true&\"\nvar renderjs\nimport script from \"./Professiona.vue?vue&type=script&lang=js&\"\nexport * from \"./Professiona.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Professiona.vue?vue&type=style&index=0&id=47061b1b&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"47061b1b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Professiona.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Professiona.vue?vue&type=template&id=47061b1b&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getImage()\n  var m1 = _vm.getImage()\n  var m2 = m1 ? _vm.getImage() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Professiona.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Professiona.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 列表菜单 -->\n\t\t<view class=\"menu-item\" v-for=\"(item, index) in menuItems\" :key=\"index\" @click=\"handleItemClick(item)\">\n\t\t\t<text class=\"item-text\">{{ item.name }}</text>\n\t\t\t<!-- 显示已上传的图片标志 -->\n\t\t\t<view class=\"image-status\">\n\t\t\t\t<text v-if=\"form[item.imgField]\" class=\"uploaded\">已上传</text>\n\t\t\t\t<text class=\"arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 弹窗 -->\n\t\t<u-modal\n\t\t\t:show=\"showModal\"\n\t\t\t:title=\"'开通' + selectedItem.name\"\n\t\t\t:showCancelButton=\"true\"\n\t\t\tconfirmText=\"确定\"\n\t\t\tcancelText=\"取消\"\n\t\t\t@confirm=\"confirmUpload\"\n\t\t\t@cancel=\"closeModal\"\n\t\t>\n\t\t\t<view class=\"modal-content\">\n\t\t\t\t<view class=\"upload-box\">\n\t\t\t\t\t<!-- 只在没有图片时显示上传按钮 -->\n\t\t\t\t\t<view v-if=\"!getImage()\" class=\"upload-button\" @click=\"chooseImage\">\n\t\t\t\t\t\t<view class=\"upload-icon\">\n\t\t\t\t\t\t\t<text class=\"icon\">+</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"upload-text\">上传证件图片</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 显示上传的图片 -->\n\t\t\t\t\t<view v-if=\"getImage()\" class=\"image-preview\">\n\t\t\t\t\t\t<image :src=\"getImage()\" mode=\"aspectFit\" style=\"width: 200px; height: 200px;\"></image>\n\t\t\t\t\t\t<view class=\"delete-button\" @click=\"deleteImage\">删除</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-modal>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tmenuItems: [\n\t\t\t\t{ name: '电工证', imgField: 'electricianImg' },\n\t\t\t\t{ name: '驾驶证', imgField: 'driverLicenseImg' },\n\t\t\t\t{ name: '制冷与空调作业证', imgField: 'workPermitImg' },\n\t\t\t\t{ name: '燃气具安装维修资质', imgField: 'maintenanceCertificateImg' },\n\t\t\t\t{ name: '高空作业A类证', imgField: 'gkzyAImg' },\n\t\t\t\t{ name: '高空作业B类证', imgField: 'gkzyBImg' },\n\t\t\t\t{ name: '开锁备案', imgField: 'ksbaImg' },\n\t\t\t\t{ name: '焊工证', imgField: 'hgzImg' },\n\t\t\t\t{ name: '弱电A类证', imgField: 'rdAImg' },\n\t\t\t\t{ name: '弱电B类证', imgField: 'rdBImg' },\n\t\t\t\t{ name: '特种行业许可证', imgField: 'tzhyhkzImg' },\n\t\t\t\t{ name: '其他', imgField: 'otherImg' }\n\t\t\t],\n\t\t\tshowModal: false,\n\t\t\tInfo: {},\n\t\t\tselectedItem: {},\n\t\t\tform: {\n\t\t\t\telectricianImg: '',\n\t\t\t\tdriverLicenseImg: '',\n\t\t\t\tworkPermitImg: '',\n\t\t\t\tmaintenanceCertificateImg: '',\n\t\t\t\tgkzyAImg: '',\n\t\t\t\tgkzyBImg: '',\n\t\t\t\tksbaImg: '',\n\t\t\t\thgzImg: '',\n\t\t\t\trdAImg: '',\n\t\t\t\trdBImg: '',\n\t\t\t\ttzhyhkzImg: '',\n\t\t\t\totherImg: '',\n\t\t\t},\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tthis.fetchMasterData();\n\t},\n\tmethods: {\n\t\tasync fetchMasterData() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.shifu.getCertList();\n\t\t\t\tif (res && res.data) {\n\t\t\t\t\tconsole.log('API Response:', res.data);\n\t\t\t\t\t\n\t\t\t\t\t// Set Info with the complete response data\n\t\t\t\t\tthis.Info = {...res.data};\n\t\t\t\t\t\n\t\t\t\t\t// Initialize form with all image fields\n\t\t\t\t\tconst formData = {};\n\t\t\t\t\tthis.menuItems.forEach(item => {\n\t\t\t\t\t\tconst imageValue = res.data[item.imgField] && res.data[item.imgField] !== 'null' \n\t\t\t\t\t\t\t? res.data[item.imgField] \n\t\t\t\t\t\t\t: '';\n\t\t\t\t\t\tformData[item.imgField] = imageValue;\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tthis.form = {...this.form, ...formData};\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('No data received from getCertList');\n\t\t\t\t\tthis.$util.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '获取证件信息失败',\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Error fetching certificate data:', error);\n\t\t\t\tthis.$util.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\thandleItemClick(item) {\n\t\t\tthis.selectedItem = item;\n\t\t\tthis.showModal = true;\n\t\t},\n\t\tcloseModal() {\n\t\t\tthis.showModal = false;\n\t\t},\n\t\tgetImage() {\n\t\t\treturn this.form[this.selectedItem.imgField] || '';\n\t\t},\n\t\tchooseImage() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tawait this.uploadImage(res.tempFilePaths[0]);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('选择图片失败:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tasync uploadImage(tempFilePath) {\n\t\t\tthis.$util.showLoading({\n\t\t\t\ttitle: '上传中'\n\t\t\t});\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst response = await this.$api.base.uploadFile({\n\t\t\t\t\tfilePath: tempFilePath,\n\t\t\t\t\tname: 'multipartFile',\n\t\t\t\t\tformData: {\n\t\t\t\t\t\ttype: 'picture',\n\t\t\t\t\t},\n\t\t\t\t});\n\n\t\t\t\tif (response) {\n\t\t\t\t\tthis.$set(this.form, this.selectedItem.imgField, response);\n\t\t\t\t\tthis.$util.hideAll();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response?.msg || '上传失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tthis.$util.hideAll();\n\t\t\t\tconsole.error('上传失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '上传失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\tasync deleteImage() {\r\n\t  try {\r\n\t    this.$util.showLoading({ title: '删除中...' });\r\n\t    \r\n\t    // Prepare data for deletion\r\n\t    const deleteData = {\r\n\t      ...this.Info,\r\n\t      [this.selectedItem.imgField]: null // Set the image field to empty\r\n\t    };\r\n\t    \r\n\t    // Call delete API\r\n\t    const res = await this.$api.shifu.delCertList(deleteData);\r\n\t    \r\n\t    // Handle successful deletion (even if response is just a string)\r\n\t    if (res.code ==='200' ) {\r\n\t      // Update local state\r\n\t      this.$set(this.form, this.selectedItem.imgField, null);\r\n\t      this.Info[this.selectedItem.imgField] = null;\r\n\t      \r\n\t      // Show success message\r\n\t      this.$util.showToast({\r\n\t        title: '删除成功',\r\n\t        icon: 'success'\r\n\t      });\r\n\t      \r\n\t      // Close the modal immediately\r\n\t      this.showModal = false;\r\n\t      \r\n\t      // Optional: refresh data from server\r\n\t      await this.fetchMasterData();\r\n\t    } else {\r\n\t      // If response doesn't indicate success\r\n\t      throw new Error(res?.msg || '删除失败');\r\n\t    }\r\n\t  } catch (error) {\r\n\t    console.error('删除操作:', error);\r\n\t    // Special handling for \"删除成功\" message that comes as error\r\n\t    if (error.message.includes('删除成功')) {\r\n\t      this.$set(this.form, this.selectedItem.imgField, null);\r\n\t      this.Info[this.selectedItem.imgField] = null;\r\n\t      this.showModal = false;\r\n\t      this.$util.showToast({\r\n\t        title: '删除成功',\r\n\t        icon: 'success'\r\n\t      });\r\n\t    } else {\r\n\t      this.$util.showToast({\r\n\t        icon: 'error',\r\n\t        title: error.message || '删除失败，请重试',\r\n\t      });\r\n\t    }\r\n\t  } finally {\r\n\t    this.$util.hideAll();\r\n\t  }\r\n\t},\n\t\tasync confirmUpload() {\n\t\t\tconst currentImage = this.form[this.selectedItem.imgField];\n\t\t\tif (!currentImage) {\n\t\t\t\tthis.$util.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请上传证件图片',\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tconst updatedInfo = {\n\t\t\t\t\t...this.Info,\n\t\t\t\t\t[this.selectedItem.imgField]: currentImage,\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tthis.$set(this, 'Info', updatedInfo);\n\t\t\t\t\n\t\t\t\tconst res = await this.$api.shifu.postCertList(this.Info);\n\t\t\t\tif(res === \"信息修改成功，请等待审核\"){\n\t\t\t\t\tthis.$util.showToast({\n\t\t\t\t\t\ttitle: '信息修改成功，请等待审核',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.showModal = false;\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Upload error:', error);\n\t\t\t\tthis.$util.showToast({\n\t\t\t\t\ticon: 'error',\n\t\t\t\t\ttitle: '上传失败，请重试',\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.header {\n\twidth: 750rpx;\n\theight: 58rpx;\n\tbackground: #fff7f1;\n\tline-height: 58rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 400;\n}\n\n.container {\n\tbackground-color: #f7f9fc;\n\tmin-height: 100vh;\n\tpadding: 10px;\n}\n\n.menu-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 12px 16px;\n\tbackground-color: #ffffff;\n\tborder-radius: 8px;\n\tmargin-bottom: 8px;\n\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.item-text {\n\tfont-size: 16px;\n\tcolor: #2c3e50;\n\tfont-weight: 500;\n}\n\n.image-status {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.uploaded {\n\tcolor: #28a745;\n\tfont-size: 14px;\n\tmargin-right: 8px;\n}\n\n.arrow {\n\tcolor: #7f8c8d;\n\tfont-size: 16px;\n}\n\n.modal-content {\n\tpadding: 20px;\n\ttext-align: center;\n}\n\n.upload-box {\n\tmargin: 15px 0;\n}\n\n.upload-button {\n\twidth: 200px;\n\theight: 200px;\n\tborder: 1px dashed #cccccc;\n\tborder-radius: 8px;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin: 0 auto;\n\tcursor: pointer;\n}\n\n.upload-icon {\n\twidth: 60px;\n\theight: 60px;\n\tborder-radius: 30px;\n\tbackground-color: #f2f2f2;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 10px;\n}\n\n.icon {\n\tfont-size: 36px;\n\tcolor: #999999;\n}\n\n.upload-text {\n\tfont-size: 14px;\n\tcolor: #666666;\n}\n\n.image-preview {\n\tposition: relative;\n\twidth: 200px;\n\tmargin: 0 auto;\n}\n\n.delete-button {\n\tposition: absolute;\n\tbottom: -30px;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tbackground-color: #ff4d4f;\n\tcolor: white;\n\tpadding: 5px 15px;\n\tborder-radius: 4px;\n\tfont-size: 14px;\n\tcursor: pointer;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Professiona.vue?vue&type=style&index=0&id=47061b1b&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Professiona.vue?vue&type=style&index=0&id=47061b1b&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755676968544\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}