@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-7ae43980 {
  background-color: #F8F8F8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.header.data-v-7ae43980 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 88rpx;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.header .back-btn.data-v-7ae43980 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .back-btn .back-icon.data-v-7ae43980 {
  font-size: 36rpx;
  color: #333;
}
.header .title.data-v-7ae43980 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.header .placeholder.data-v-7ae43980 {
  width: 60rpx;
}
.content.data-v-7ae43980 {
  padding: 88rpx 30rpx 20rpx;
}
.order-info.data-v-7ae43980 {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.order-info .order-header.data-v-7ae43980 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-info .order-header .order-no.data-v-7ae43980 {
  font-size: 24rpx;
  color: #666;
}
.order-info .order-header .order-status.data-v-7ae43980 {
  font-size: 24rpx;
  color: #2E80FE;
  font-weight: 500;
}
.order-info .order-detail .goods-info.data-v-7ae43980 {
  display: flex;
  align-items: center;
}
.order-info .order-detail .goods-info .goods-image.data-v-7ae43980 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.order-info .order-detail .goods-info .goods-text.data-v-7ae43980 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.order-info .order-detail .goods-info .goods-text .goods-name.data-v-7ae43980 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.order-info .order-detail .goods-info .goods-text .goods-price.data-v-7ae43980 {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 600;
}
.form-container.data-v-7ae43980 {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.form-container .form-title.data-v-7ae43980 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.warranty-input-group.data-v-7ae43980 {
  display: flex;
  align-items: center;
}
.warranty-input-group .unit-text.data-v-7ae43980 {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}
.footer.data-v-7ae43980 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}
.footer .btn-cancel.data-v-7ae43980,
.footer .btn-confirm.data-v-7ae43980 {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.2s;
}
.footer .btn-cancel.data-v-7ae43980 {
  background: #f8f8f8;
  color: #666;
}
.footer .btn-confirm.data-v-7ae43980 {
  background: #2E80FE;
  color: #fff;
}
.footer .btn-cancel.data-v-7ae43980:active {
  background: #e8e8e8;
}
.footer .btn-confirm.data-v-7ae43980:active {
  background: #1a6fd1;
}
.sub_orders.data-v-7ae43980 {
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.sub_orders .sub_title.data-v-7ae43980 {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 20rpx;
}
.sub_orders .sub_scroll_container.data-v-7ae43980 {
  max-height: 450rpx;
  overflow-y: auto;
}
.sub_orders .sub_item.data-v-7ae43980 {
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.sub_orders .sub_item .sub_head.data-v-7ae43980 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.sub_orders .sub_item .sub_head .sub_no.data-v-7ae43980 {
  font-size: 22rpx;
  color: #666;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.sub_orders .sub_item .sub_head .sub_status.data-v-7ae43980 {
  font-size: 22rpx;
  color: #2E80FE;
  font-weight: 500;
}
.sub_orders .sub_item .sub_content.data-v-7ae43980 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.sub_orders .sub_item .sub_content .sub_info_grid.data-v-7ae43980 {
  flex: 1;
  margin-right: 20rpx;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row.data-v-7ae43980 {
  display: flex;
  margin-bottom: 12rpx;
  gap: 20rpx;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row.data-v-7ae43980:last-child {
  margin-bottom: 0;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item.data-v-7ae43980 {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item.sub_reason_item.data-v-7ae43980 {
  flex: 2;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_label.data-v-7ae43980 {
  font-size: 20rpx;
  color: #999;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.data-v-7ae43980 {
  font-size: 22rpx;
  color: #333;
  flex: 1;
  min-width: 0;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_amount_value.data-v-7ae43980 {
  font-weight: 500;
  color: #ff6b35;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_warranty-value.data-v-7ae43980 {
  color: #2E80FE;
  font-weight: 500;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_reason_value.data-v-7ae43980 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.sub_orders .sub_item .sub_content .sub_info_grid .sub_info_row .sub_info_item .sub_value.sub_time_value.data-v-7ae43980 {
  font-size: 20rpx;
  color: #999;
}
.sub_orders .sub_item .sub_content .sub_actions.data-v-7ae43980 {
  flex-shrink: 0;
  align-self: flex-start;
}
.sub_orders .sub_item .sub_content .sub_actions .sub_qzf.data-v-7ae43980 {
  width: 120rpx;
  height: 40rpx;
  background: #ff6b6b;
  border-radius: 40rpx;
  font-size: 18rpx;
  font-weight: 400;
  line-height: 40rpx;
  text-align: center;
  color: #fff;
}
.upload-container.data-v-7ae43980 {
  margin-top: 16rpx;
  padding: 20rpx;
  border: 1rpx dashed #ccc;
  border-radius: 8rpx;
  background: #fafafa;
}
.parts-img.data-v-7ae43980 {
  width: 120rpx;
  height: 120rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
}
.reason-type-display.data-v-7ae43980 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}
.reason-type-text.data-v-7ae43980 {
  font-size: 28rpx;
  color: #333;
}
.reason-type-badge.data-v-7ae43980 {
  background: #2E80FE;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.data-v-7ae43980 .u-form-item {
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
}
.data-v-7ae43980 .u-form-item:not(.last-form-item) {
  border-bottom: 1rpx solid #f0f0f0;
}
.data-v-7ae43980 .u-form-item:last-child {
  margin-bottom: 0;
}
.data-v-7ae43980 .u-form-item__body {
  padding-left: 0 !important;
}
.data-v-7ae43980 .u-form-item__label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.data-v-7ae43980 .u--input__content {
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.data-v-7ae43980 .u--textarea__content {
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
}

